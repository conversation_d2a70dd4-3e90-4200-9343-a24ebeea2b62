FROM python:3.9.18-alpine3.19

LABEL maintainer="DKY dukai <<EMAIL>>"
LABEL build_date="20250708"
LABEL build_version="v2.0.0"
LABEL description="Network Port Exporter with VM Monitoring and Topology Generation"

WORKDIR /app

# 复制应用文件
COPY app.py /app/
COPY config.json /app/
COPY README.md /app/

# 更换为阿里云镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装系统依赖
RUN apk update && \
    apk add --no-cache \
    bash \
    gcc \
    python3-dev \
    musl-dev \
    linux-headers \
    libvirt-dev \
    libvirt-client \
    util-linux \
    procps \
    curl \
    wget \
    jq

# 安装Python依赖
RUN pip install -i http://mirrors.aliyun.com/pypi/simple/ \
    --no-cache-dir \
    --trusted-host mirrors.aliyun.com \
    psutil \
    prometheus-client \
    libvirt-python \
    chardet

# 创建配置目录
RUN mkdir -p /etc/network_exporter /tmp

# 复制默认配置
RUN cp /app/config.json /etc/network_exporter/config.json

# 创建非root用户（可选，但需要特权访问）
# RUN adduser -D -s /bin/bash exporter

# 设置权限
RUN chmod +x /app/app.py

# 暴露端口
EXPOSE 9192

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9192/metrics || exit 1

# 启动命令
CMD ["python", "-u", "/app/app.py", "--config", "/etc/network_exporter/config.json"]