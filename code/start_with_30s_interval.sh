#!/bin/bash

# 启动网络监控系统，确保每30秒生成一次拓扑数据
# 作者: DKY du<PERSON> <<EMAIL>>

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}网络端口监控系统 - 30秒间隔拓扑生成${NC}"
echo "=================================================="

# 检查配置文件
CONFIG_FILE="config.json"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${YELLOW}配置文件不存在，创建默认配置...${NC}"
    cat > "$CONFIG_FILE" << EOF
{
  "port": 9192,
  "debug": true,
  "log_level": "INFO",
  "log_format": "%(asctime)s - %(levelname)s - %(message)s",
  "enable_host_monitoring": true,
  "enable_vm_monitoring": true,
  "collection_interval": 30,
  "supported_hypervisors": [
    "kvm",
    "qemu",
    "vmware",
    "hyperv"
  ],
  "libvirt_uri": "qemu:///system",
  "include_loopback": false,
  "include_ipv6": true,
  "port_range_filter": null,
  "enable_topology_generation": true,
  "topology_output_path": "/tmp/network_topology.json"
}
EOF
    echo -e "${GREEN}配置文件已创建: $CONFIG_FILE${NC}"
fi

# 验证配置
INTERVAL=$(grep -o '"collection_interval": [0-9]*' "$CONFIG_FILE" | grep -o '[0-9]*')
TOPOLOGY_ENABLED=$(grep -o '"enable_topology_generation": [a-z]*' "$CONFIG_FILE" | grep -o '[a-z]*')
TOPOLOGY_PATH=$(grep -o '"topology_output_path": "[^"]*"' "$CONFIG_FILE" | sed 's/"topology_output_path": "\(.*\)"/\1/')

echo "当前配置:"
echo "  - 收集间隔: ${INTERVAL}秒"
echo "  - 拓扑生成: $TOPOLOGY_ENABLED"
echo "  - 输出路径: $TOPOLOGY_PATH"
echo

if [[ "$INTERVAL" != "30" ]]; then
    echo -e "${YELLOW}警告: 收集间隔不是30秒，当前为${INTERVAL}秒${NC}"
fi

if [[ "$TOPOLOGY_ENABLED" != "true" ]]; then
    echo -e "${YELLOW}警告: 拓扑生成未启用${NC}"
fi

# 创建输出目录
OUTPUT_DIR=$(dirname "$TOPOLOGY_PATH")
mkdir -p "$OUTPUT_DIR"

# 清理旧的拓扑文件
if [[ -f "$TOPOLOGY_PATH" ]]; then
    echo "清理旧的拓扑文件: $TOPOLOGY_PATH"
    rm -f "$TOPOLOGY_PATH"
fi

echo
echo -e "${BLUE}启动监控系统...${NC}"
echo "拓扑数据将每30秒生成一次"
echo "按 Ctrl+C 停止"
echo

# 在后台启动监控脚本
if [[ -f "test_topology_generation.py" ]]; then
    echo "启动拓扑监控脚本..."
    python3 test_topology_generation.py &
    MONITOR_PID=$!
    echo "监控脚本PID: $MONITOR_PID"
    echo
fi

# 启动主程序
echo "启动主程序..."
python3 app.py --config "$CONFIG_FILE" --debug

# 清理
if [[ -n "$MONITOR_PID" ]]; then
    echo "停止监控脚本..."
    kill $MONITOR_PID 2>/dev/null || true
fi

echo "程序已退出"
