#!/bin/bash

# 容器启动问题故障排除脚本
# <AUTHOR> <EMAIL>

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CONTAINER_NAME="node_port_exporter"
IMAGE_NAME="host_vm_port:v1.0.0"

echo -e "${BLUE}容器启动问题故障排除${NC}"
echo "=================================="

# 检查容器状态
check_container_status() {
    echo -e "${BLUE}1. 检查容器状态${NC}"
    
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "容器存在，状态信息:"
        docker ps -a | grep "$CONTAINER_NAME"
        echo
        
        # 检查容器是否在运行
        if docker ps | grep -q "$CONTAINER_NAME"; then
            echo -e "${GREEN}容器正在运行${NC}"
        else
            echo -e "${RED}容器已停止${NC}"
        fi
    else
        echo -e "${RED}容器不存在${NC}"
    fi
    echo
}

# 检查容器日志
check_container_logs() {
    echo -e "${BLUE}2. 检查容器日志${NC}"
    
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "最近的容器日志:"
        echo "----------------"
        docker logs --tail 50 "$CONTAINER_NAME" 2>&1 || echo "无法获取日志"
        echo "----------------"
    else
        echo "容器不存在，无法获取日志"
    fi
    echo
}

# 检查libvirt服务
check_libvirt_service() {
    echo -e "${BLUE}3. 检查libvirt服务${NC}"
    
    if systemctl is-active --quiet libvirtd; then
        echo -e "${GREEN}libvirtd服务正在运行${NC}"
    else
        echo -e "${RED}libvirtd服务未运行${NC}"
        echo "尝试启动libvirtd服务:"
        sudo systemctl start libvirtd || echo "启动失败"
    fi
    
    # 检查libvirt连接
    echo "测试libvirt连接:"
    if virsh list >/dev/null 2>&1; then
        echo -e "${GREEN}libvirt连接正常${NC}"
        echo "运行中的虚拟机:"
        virsh list --state-running
    else
        echo -e "${RED}libvirt连接失败${NC}"
    fi
    echo
}

# 检查挂载点
check_mounts() {
    echo -e "${BLUE}4. 检查挂载点${NC}"
    
    local mounts=(
        "/var/run/libvirt"
        "/var/run"
        "/proc"
    )
    
    for mount in "${mounts[@]}"; do
        if [[ -d "$mount" ]]; then
            echo -e "${GREEN}✓${NC} $mount 存在"
        else
            echo -e "${RED}✗${NC} $mount 不存在"
        fi
    done
    echo
}

# 检查端口占用
check_port() {
    echo -e "${BLUE}5. 检查端口占用${NC}"
    
    local port=9192
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo -e "${YELLOW}端口 $port 已被占用:${NC}"
        netstat -tlnp 2>/dev/null | grep ":$port "
    else
        echo -e "${GREEN}端口 $port 可用${NC}"
    fi
    echo
}

# 测试容器启动
test_container_start() {
    echo -e "${BLUE}6. 测试容器启动${NC}"
    
    # 停止现有容器
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "停止现有容器..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # 使用调试模式启动容器
    echo "使用调试模式启动容器..."
    docker run --name "${CONTAINER_NAME}_debug" \
        -v /var/run/libvirt:/var/run/libvirt \
        -v /var/run:/var/run \
        -v /proc:/host_proc \
        --privileged \
        --network host \
        -e PYTHONUNBUFFERED=1 \
        "$IMAGE_NAME" \
        python -u app.py --debug --config /app/config_debug.json &
    
    local container_pid=$!
    echo "容器启动PID: $container_pid"
    
    # 等待几秒钟查看启动情况
    sleep 10
    
    echo "检查调试容器状态:"
    if docker ps | grep -q "${CONTAINER_NAME}_debug"; then
        echo -e "${GREEN}调试容器启动成功${NC}"
        echo "调试容器日志:"
        docker logs "${CONTAINER_NAME}_debug" 2>&1 | tail -20
    else
        echo -e "${RED}调试容器启动失败${NC}"
        echo "调试容器日志:"
        docker logs "${CONTAINER_NAME}_debug" 2>&1 || echo "无法获取日志"
    fi
    
    # 清理调试容器
    echo "清理调试容器..."
    docker stop "${CONTAINER_NAME}_debug" 2>/dev/null || true
    docker rm "${CONTAINER_NAME}_debug" 2>/dev/null || true
    echo
}

# 提供解决方案
provide_solutions() {
    echo -e "${BLUE}7. 可能的解决方案${NC}"
    echo
    echo "基于检查结果，尝试以下解决方案:"
    echo
    echo "1. 如果libvirt连接失败:"
    echo "   sudo systemctl start libvirtd"
    echo "   sudo systemctl enable libvirtd"
    echo
    echo "2. 如果端口被占用:"
    echo "   修改配置文件中的端口号"
    echo "   或停止占用端口的进程"
    echo
    echo "3. 如果权限问题:"
    echo "   确保Docker以特权模式运行 (--privileged)"
    echo "   检查挂载目录的权限"
    echo
    echo "4. 如果配置问题:"
    echo "   使用调试配置文件:"
    echo "   docker run ... -v \$(pwd)/config_debug.json:/etc/network_exporter/config.json ..."
    echo
    echo "5. 重新构建镜像:"
    echo "   docker build -t host_vm_port:v1.0.0 ."
    echo
    echo "6. 使用修复后的启动命令:"
    echo "   docker run --name node_port_exporter \\"
    echo "     -v /var/run/libvirt:/var/run/libvirt:ro \\"
    echo "     -v /var/run:/var/run:ro \\"
    echo "     -v /proc:/host_proc:ro \\"
    echo "     -v \$(pwd)/config_debug.json:/etc/network_exporter/config.json:ro \\"
    echo "     --restart=unless-stopped \\"
    echo "     --privileged \\"
    echo "     --network host \\"
    echo "     -d host_vm_port:v1.0.0"
}

# 主函数
main() {
    check_container_status
    check_container_logs
    check_libvirt_service
    check_mounts
    check_port
    test_container_start
    provide_solutions
    
    echo -e "${GREEN}故障排除完成${NC}"
}

# 执行主函数
main "$@"
