#!/bin/bash

# Docker构建问题修复脚本
# 解决procfs-ng包不存在的问题

set -e

echo "修复Docker构建问题..."

# 备份原始Dockerfile
if [[ -f "Dockerfile" ]] && [[ ! -f "Dockerfile.backup" ]]; then
    cp Dockerfile Dockerfile.backup
    echo "已备份原始Dockerfile为Dockerfile.backup"
fi

# 检查并修复Dockerfile中的包名问题
if grep -q "procfs-ng" Dockerfile 2>/dev/null; then
    echo "发现procfs-ng包，正在替换为procps..."
    sed -i 's/procfs-ng/procps/g' Dockerfile
    echo "已将procfs-ng替换为procps"
fi

# 检查并添加必要的包
if ! grep -q "curl" Dockerfile 2>/dev/null; then
    echo "添加curl包..."
    sed -i '/util-linux/a\    curl \\' Dockerfile
fi

if ! grep -q "jq" Dockerfile 2>/dev/null; then
    echo "添加jq包..."
    sed -i '/curl/a\    jq \\' Dockerfile
fi

# 修复健康检查命令
if grep -q "wget" Dockerfile 2>/dev/null; then
    echo "修复健康检查命令..."
    sed -i 's/wget --no-verbose --tries=1 --spider/curl -f/g' Dockerfile
fi

# 显示修复后的Dockerfile内容
echo
echo "修复后的Dockerfile关键部分:"
echo "================================"
grep -A 15 "RUN apk update" Dockerfile || echo "未找到apk update部分"
echo
grep -A 2 "HEALTHCHECK" Dockerfile || echo "未找到HEALTHCHECK部分"
echo "================================"

echo
echo "修复完成！现在可以尝试构建Docker镜像："
echo "  docker build -t network_exporter:v2.0.0 ."
echo
echo "或者使用QGA专用版本："
echo "  docker build -f Dockerfile.qga -t network_exporter_qga:v2.0.0 ."
echo
echo "或者使用部署脚本："
echo "  chmod +x deploy_qga.sh"
echo "  ./deploy_qga.sh deploy"
