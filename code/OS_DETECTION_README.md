# 虚拟机操作系统检测功能

## 概述

改进后的`_detect_vm_os`方法使用多种策略来准确检测虚拟机的操作系统类型，支持Linux和Windows虚拟机的自动识别。

## 检测策略

### 1. QGA检测（最准确）
通过QEMU Guest Agent执行系统命令来检测操作系统：

**Linux检测：**
- 执行 `uname -s` 命令
- 检查输出中是否包含 "Linux"

**Windows检测：**
- 执行 `cmd.exe /c ver` 命令
- 检查输出中是否包含 "Windows" 或 "Microsoft"

### 2. XML描述检测
分析libvirt虚拟机XML配置：

**Windows指标：**
- `windows`, `win10`, `win11`, `win7`, `win8`, `winxp`
- `win2019`, `win2016`, `win2012`
- `microsoft`, `ms-dos`, `type="windows"`
- `virtio-win` 驱动

**Linux指标：**
- `linux`, `ubuntu`, `centos`, `rhel`, `debian`
- `fedora`, `suse`, `opensuse`
- `type="linux"`, `kernel`, `vmlinuz`

**磁盘镜像检测：**
- 分析磁盘镜像文件名中的操作系统标识

### 3. 名称模式检测
根据虚拟机名称模式判断：

**Windows模式：**
- `windows`, `win10`, `win11`, `win7`, `win8`
- `microsoft`, `ms`, `server2019`, `server2016`

**Linux模式：**
- `linux`, `ubuntu`, `centos`, `rhel`, `debian`
- `fedora`, `suse`, `redhat`, `mint`, `arch`

## 检测流程

```
1. QGA检测 (最优先)
   ├── 检查QGA可用性
   ├── 测试Linux命令
   ├── 测试Windows命令
   └── 返回结果

2. XML检测 (次优先)
   ├── 获取虚拟机XML描述
   ├── 搜索操作系统指标
   ├── 分析磁盘镜像文件名
   └── 返回结果

3. 名称检测 (最后)
   ├── 分析虚拟机名称
   ├── 匹配操作系统模式
   └── 返回结果

4. 返回 'unknown' (如果都失败)
```

## 使用方法

### 测试OS检测功能

```bash
# 完整的OS检测测试
python test_os_detection.py

# 简单的OS检测测试
python test_vm_os_simple.py

# 使用专用配置运行应用
python app.py --config config_os_detection.json
```

### 配置选项

在配置文件中可以调整QGA相关设置：

```json
{
  "qga_settings": {
    "enable_qga": true,
    "qga_timeout": 30,
    "fallback_to_nsenter": true,
    "retry_attempts": 3,
    "qga_ping_timeout": 10
  }
}
```

## 日志输出

启用DEBUG日志级别可以看到详细的检测过程：

```
2025-07-08 10:30:15,123 - DEBUG - _detect_vm_os:231 - Detecting OS type for VM: ubuntu-vm
2025-07-08 10:30:15,124 - DEBUG - _detect_os_via_qga:245 - QGA available for ubuntu-vm, starting OS detection
2025-07-08 10:30:15,567 - DEBUG - _test_linux_command:289 - Linux detected via uname for ubuntu-vm
2025-07-08 10:30:15,568 - INFO - _detect_vm_os:235 - VM ubuntu-vm OS detected via QGA: linux
```

## 故障排除

### QGA不可用
如果QGA检测失败，系统会自动回退到XML和名称检测：

```bash
# 检查QGA状态
virsh qemu-agent-command <vm-name> '{"execute":"guest-ping"}'

# 确保虚拟机内安装了qemu-guest-agent
# Linux: sudo apt-get install qemu-guest-agent
# Windows: 安装QEMU Guest Agent for Windows
```

### XML检测不准确
如果XML检测不准确，可以：

1. 检查虚拟机XML配置：
   ```bash
   virsh dumpxml <vm-name>
   ```

2. 确保XML中包含操作系统相关信息

3. 使用描述性的磁盘镜像文件名

### 名称检测失败
使用包含操作系统标识的虚拟机名称：
- 好的例子：`ubuntu-20.04-server`, `windows-10-desktop`
- 不好的例子：`vm1`, `test-machine`

## 性能考虑

- QGA检测需要执行命令，可能需要几秒钟
- XML检测速度最快，几乎瞬时完成
- 名称检测速度最快，但准确性最低

## 扩展性

可以通过修改以下方法来添加新的检测模式：

1. `_detect_os_via_qga()` - 添加新的QGA命令
2. `_detect_os_via_xml()` - 添加新的XML指标
3. `_detect_os_via_name_pattern()` - 添加新的名称模式

## 示例输出

```
=== QGA检测 ubuntu-vm ===
QGA可用，开始OS检测...
  测试Linux命令 (uname -s)...
    ✓ Linux检测成功: ['Linux']

=== XML检测 windows-vm ===
分析XML内容...
  找到指标: ['Windows: windows', 'Windows: virtio-win']
    Windows磁盘镜像: /var/lib/libvirt/images/windows-10.qcow2

=== 名称检测 centos-server ===
  找到模式: ['Linux: centos']

--- 检测结果总结 ---
ubuntu-vm        -> linux      (QGA:linux, XML:unknown, Name:ubuntu)
windows-vm       -> windows    (QGA:unknown, XML:windows, Name:windows)
centos-server    -> linux      (QGA:unknown, XML:unknown, Name:centos)
```
