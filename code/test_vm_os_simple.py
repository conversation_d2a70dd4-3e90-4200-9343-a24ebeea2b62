#!/usr/bin/env python3
"""
简单的虚拟机OS检测测试
直接测试改进后的_detect_vm_os方法
"""

import sys
import os
import json
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入应用模块
try:
    from app import LibvirtAdapter, Config, config
    import libvirt
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在正确的目录中运行此脚本")
    sys.exit(1)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )

def test_os_detection():
    """测试OS检测功能"""
    print("=" * 60)
    print("虚拟机操作系统检测测试")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 创建LibvirtAdapter实例
        adapter = LibvirtAdapter("test-host", "qemu:///system")
        
        if not adapter.is_available():
            print("Libvirt连接不可用")
            return
        
        print("Libvirt连接成功")
        
        # 获取虚拟机列表
        vms = adapter.get_vms()
        print(f"找到 {len(vms)} 个虚拟机")
        
        if not vms:
            print("没有找到运行中的虚拟机")
            return
        
        # 测试每个虚拟机的OS检测
        for vm_info in vms:
            print(f"\n{'='*40}")
            print(f"测试虚拟机: {vm_info.name}")
            print(f"虚拟机ID: {vm_info.id}")
            print(f"当前OS类型: {vm_info.os_type}")
            print(f"{'='*40}")
            
            # 获取domain对象进行详细测试
            try:
                conn = libvirt.openReadOnly("qemu:///system")
                domain = conn.lookupByName(vm_info.name)
                
                # 测试改进后的OS检测
                detected_os = adapter._detect_vm_os(domain)
                print(f"检测到的OS类型: {detected_os}")
                
                # 显示检测过程的详细信息
                print("\n检测方法详情:")
                
                # QGA检测
                qga_result = adapter._detect_os_via_qga(vm_info.name)
                print(f"  QGA检测: {qga_result}")
                
                # XML检测
                xml_result = adapter._detect_os_via_xml(domain)
                print(f"  XML检测: {xml_result}")
                
                # 名称检测
                name_result = adapter._detect_os_via_name_pattern(vm_info.name)
                print(f"  名称检测: {name_result}")
                
                # 比较结果
                if detected_os != vm_info.os_type:
                    print(f"\n⚠️  OS类型已更新: {vm_info.os_type} -> {detected_os}")
                else:
                    print(f"\n✓  OS类型一致: {detected_os}")
                
                conn.close()
                
            except Exception as e:
                logger.error(f"测试虚拟机 {vm_info.name} 时出错: {e}")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_individual_methods():
    """测试各个检测方法"""
    print("\n" + "=" * 60)
    print("测试各个检测方法")
    print("=" * 60)
    
    setup_logging()
    
    try:
        adapter = LibvirtAdapter("test-host", "qemu:///system")
        
        if not adapter.is_available():
            print("Libvirt连接不可用")
            return
        
        # 获取第一个虚拟机进行详细测试
        vms = adapter.get_vms()
        if not vms:
            print("没有找到虚拟机")
            return
        
        vm_info = vms[0]
        print(f"使用虚拟机 {vm_info.name} 进行详细测试")
        
        conn = libvirt.openReadOnly("qemu:///system")
        domain = conn.lookupByName(vm_info.name)
        
        print(f"\n1. QGA检测测试:")
        print(f"   QGA可用性: {adapter._is_qga_available(vm_info.name)}")
        if adapter._is_qga_available(vm_info.name):
            print(f"   Linux命令测试: {adapter._test_linux_command(vm_info.name)}")
            print(f"   Windows命令测试: {adapter._test_windows_command(vm_info.name)}")
        
        print(f"\n2. XML检测测试:")
        xml_result = adapter._detect_os_via_xml(domain)
        print(f"   XML检测结果: {xml_result}")
        
        print(f"\n3. 名称检测测试:")
        name_result = adapter._detect_os_via_name_pattern(vm_info.name)
        print(f"   名称检测结果: {name_result}")
        
        conn.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    # 加载配置
    global config
    if os.path.exists('config_os_detection.json'):
        config = Config.from_file('config_os_detection.json')
        print("已加载OS检测专用配置")
    
    # 运行测试
    test_os_detection()
    test_individual_methods()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
