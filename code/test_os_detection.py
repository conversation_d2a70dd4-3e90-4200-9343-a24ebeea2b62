#!/usr/bin/env python3
"""
虚拟机操作系统检测测试脚本
测试改进后的OS检测功能
"""

import json
import subprocess
import sys
import time
import xml.etree.ElementTree as ET
from typing import List, Dict, Any

def run_command(command: List[str], timeout: int = 10) -> List[str]:
    """执行命令并返回输出"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=timeout)
        return result.stdout.strip().splitlines()
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {' '.join(command)}")
        print(f"错误: {e.stderr}")
        return []
    except subprocess.TimeoutExpired:
        print(f"命令超时: {' '.join(command)}")
        return []
    except FileNotFoundError:
        print(f"命令未找到: {command[0]}")
        return []

def get_vm_list() -> List[str]:
    """获取运行中的虚拟机列表"""
    print("获取运行中的虚拟机列表...")
    result = run_command(['virsh', 'list', '--state-running', '--name'])
    vms = [vm.strip() for vm in result if vm.strip()]
    print(f"找到 {len(vms)} 个运行中的虚拟机: {vms}")
    return vms

def detect_os_via_qga(vm_name: str) -> str:
    """通过QGA检测操作系统"""
    print(f"\n=== QGA检测 {vm_name} ===")
    
    # 检查QGA可用性
    ping_cmd = {"execute": "guest-ping"}
    cmd_json = json.dumps(ping_cmd)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=5)
    
    if not result:
        print("QGA不可用")
        return 'unknown'
    
    try:
        response = json.loads('\n'.join(result))
        if 'error' in response:
            print(f"QGA错误: {response['error']}")
            return 'unknown'
        if 'return' not in response:
            print("QGA ping失败")
            return 'unknown'
    except json.JSONDecodeError:
        print("QGA响应解析失败")
        return 'unknown'
    
    print("QGA可用，开始OS检测...")
    
    # 测试Linux命令
    linux_result = test_linux_command(vm_name)
    if linux_result:
        return 'linux'
    
    # 测试Windows命令
    windows_result = test_windows_command(vm_name)
    if windows_result:
        return 'windows'
    
    return 'unknown'

def test_linux_command(vm_name: str) -> bool:
    """测试Linux命令"""
    print("  测试Linux命令 (uname -s)...")
    
    exec_cmd = {
        "execute": "guest-exec",
        "arguments": {
            "path": "/bin/uname",
            "arg": ["-s"],
            "capture-output": True
        }
    }
    
    cmd_json = json.dumps(exec_cmd)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=10)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response and 'pid' in response['return']:
                pid = response['return']['pid']
                output = get_exec_output(vm_name, pid)
                if output and 'Linux' in '\n'.join(output):
                    print(f"    ✓ Linux检测成功: {output}")
                    return True
                else:
                    print(f"    ✗ Linux检测失败: {output}")
        except json.JSONDecodeError as e:
            print(f"    ✗ 响应解析失败: {e}")
    
    return False

def test_windows_command(vm_name: str) -> bool:
    """测试Windows命令"""
    print("  测试Windows命令 (ver)...")
    
    exec_cmd = {
        "execute": "guest-exec",
        "arguments": {
            "path": "cmd.exe",
            "arg": ["/c", "ver"],
            "capture-output": True
        }
    }
    
    cmd_json = json.dumps(exec_cmd)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=10)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response and 'pid' in response['return']:
                pid = response['return']['pid']
                output = get_exec_output(vm_name, pid)
                if output and ('Windows' in '\n'.join(output) or 'Microsoft' in '\n'.join(output)):
                    print(f"    ✓ Windows检测成功: {output}")
                    return True
                else:
                    print(f"    ✗ Windows检测失败: {output}")
        except json.JSONDecodeError as e:
            print(f"    ✗ 响应解析失败: {e}")
    
    return False

def get_exec_output(vm_name: str, pid: int) -> List[str]:
    """获取QGA命令执行输出"""
    status_cmd = {
        "execute": "guest-exec-status",
        "arguments": {"pid": pid}
    }
    
    max_wait = 10
    wait_count = 0
    
    while wait_count < max_wait:
        cmd_json = json.dumps(status_cmd)
        virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
        result = run_command(virsh_cmd, timeout=5)
        
        if result:
            try:
                response = json.loads('\n'.join(result))
                if response.get('return', {}).get('exited', False):
                    if 'out-data' in response['return']:
                        import base64
                        output_data = base64.b64decode(response['return']['out-data']).decode('utf-8')
                        return output_data.strip().split('\n') if output_data.strip() else []
                    else:
                        return []
            except json.JSONDecodeError:
                pass
        
        time.sleep(1)
        wait_count += 1
    
    return []

def detect_os_via_xml(vm_name: str) -> str:
    """通过XML检测操作系统"""
    print(f"\n=== XML检测 {vm_name} ===")
    
    try:
        # 获取虚拟机XML描述
        result = run_command(['virsh', 'dumpxml', vm_name])
        if not result:
            print("无法获取XML描述")
            return 'unknown'
        
        xml_desc = '\n'.join(result)
        xml_lower = xml_desc.lower()
        
        print("分析XML内容...")
        
        # Windows指标
        windows_indicators = [
            'windows', 'win10', 'win11', 'win7', 'win8', 'winxp', 'win2019', 'win2016', 'win2012',
            'microsoft', 'ms-dos', 'type="windows"', 'virtio-win'
        ]
        
        # Linux指标
        linux_indicators = [
            'linux', 'ubuntu', 'centos', 'rhel', 'debian', 'fedora', 'suse', 'opensuse',
            'type="linux"', 'kernel', 'vmlinuz'
        ]
        
        found_indicators = []
        
        # 检查Windows指标
        for indicator in windows_indicators:
            if indicator in xml_lower:
                found_indicators.append(f"Windows: {indicator}")
        
        # 检查Linux指标
        for indicator in linux_indicators:
            if indicator in xml_lower:
                found_indicators.append(f"Linux: {indicator}")
        
        if found_indicators:
            print(f"  找到指标: {found_indicators}")
            
            # 判断结果
            windows_count = sum(1 for ind in found_indicators if ind.startswith('Windows'))
            linux_count = sum(1 for ind in found_indicators if ind.startswith('Linux'))
            
            if windows_count > linux_count:
                return 'windows'
            elif linux_count > windows_count:
                return 'linux'
        
        # 检查磁盘镜像文件名
        import re
        disk_sources = re.findall(r"source.*?file=['\"]([^'\"]+)['\"]", xml_desc)
        if disk_sources:
            print(f"  磁盘镜像: {disk_sources}")
            for disk_source in disk_sources:
                disk_lower = disk_source.lower()
                if any(win_name in disk_lower for win_name in ['windows', 'win10', 'win11', 'win7', 'win8']):
                    print(f"    Windows磁盘镜像: {disk_source}")
                    return 'windows'
                if any(linux_name in disk_lower for linux_name in ['ubuntu', 'centos', 'rhel', 'debian', 'linux']):
                    print(f"    Linux磁盘镜像: {disk_source}")
                    return 'linux'
        
        print("  未找到明确的OS指标")
        return 'unknown'
        
    except Exception as e:
        print(f"XML检测失败: {e}")
        return 'unknown'

def detect_os_via_name(vm_name: str) -> str:
    """通过名称检测操作系统"""
    print(f"\n=== 名称检测 {vm_name} ===")
    
    name_lower = vm_name.lower()
    
    # Windows名称模式
    windows_patterns = [
        'windows', 'win10', 'win11', 'win7', 'win8', 'winxp', 'win2019', 'win2016', 'win2012',
        'microsoft', 'ms', 'server2019', 'server2016', 'server2012'
    ]
    
    # Linux名称模式
    linux_patterns = [
        'linux', 'ubuntu', 'centos', 'rhel', 'debian', 'fedora', 'suse', 'opensuse',
        'redhat', 'mint', 'arch', 'manjaro', 'kali', 'alpine'
    ]
    
    found_patterns = []
    
    # 检查Windows模式
    for pattern in windows_patterns:
        if pattern in name_lower:
            found_patterns.append(f"Windows: {pattern}")
    
    # 检查Linux模式
    for pattern in linux_patterns:
        if pattern in name_lower:
            found_patterns.append(f"Linux: {pattern}")
    
    if found_patterns:
        print(f"  找到模式: {found_patterns}")
        
        windows_count = sum(1 for p in found_patterns if p.startswith('Windows'))
        linux_count = sum(1 for p in found_patterns if p.startswith('Linux'))
        
        if windows_count > linux_count:
            return 'windows'
        elif linux_count > windows_count:
            return 'linux'
    
    print("  未找到明确的名称模式")
    return 'unknown'

def main():
    """主函数"""
    print("=" * 60)
    print("虚拟机操作系统检测测试")
    print("=" * 60)
    
    # 获取虚拟机列表
    vms = get_vm_list()
    
    if not vms:
        print("没有找到运行中的虚拟机")
        return
    
    # 测试每个虚拟机
    results = {}
    
    for vm_name in vms:
        print(f"\n{'='*50}")
        print(f"测试虚拟机: {vm_name}")
        print(f"{'='*50}")
        
        # 三种检测方法
        qga_result = detect_os_via_qga(vm_name)
        xml_result = detect_os_via_xml(vm_name)
        name_result = detect_os_via_name(vm_name)
        
        results[vm_name] = {
            'qga': qga_result,
            'xml': xml_result,
            'name': name_result
        }
        
        # 综合判断
        methods = [qga_result, xml_result, name_result]
        windows_votes = methods.count('windows')
        linux_votes = methods.count('linux')
        
        if windows_votes > linux_votes:
            final_result = 'windows'
        elif linux_votes > windows_votes:
            final_result = 'linux'
        else:
            final_result = 'unknown'
        
        results[vm_name]['final'] = final_result
        
        print(f"\n--- {vm_name} 检测结果 ---")
        print(f"QGA检测: {qga_result}")
        print(f"XML检测: {xml_result}")
        print(f"名称检测: {name_result}")
        print(f"最终结果: {final_result}")
    
    # 总结
    print(f"\n{'='*60}")
    print("检测结果总结")
    print(f"{'='*60}")
    
    for vm_name, result in results.items():
        print(f"{vm_name:20} -> {result['final']:10} (QGA:{result['qga']}, XML:{result['xml']}, Name:{result['name']})")
    
    print(f"\n{'='*60}")
    print("测试完成")

if __name__ == "__main__":
    main()
