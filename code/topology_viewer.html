<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络拓扑可视化</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .controls button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #e9ecef;
        }
        
        .controls input[type="file"] {
            padding: 8px;
        }
        
        .topology-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            height: 600px;
            position: relative;
        }
        
        .node {
            cursor: pointer;
        }
        
        .node.physical {
            fill: #4CAF50;
        }
        
        .node.virtual {
            fill: #2196F3;
        }
        
        .node:hover {
            stroke: #333;
            stroke-width: 3px;
        }
        
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
        }
        
        .link.tcp {
            stroke: #FF5722;
        }
        
        .link.udp {
            stroke: #9C27B0;
        }
        
        .node-label {
            font-size: 12px;
            text-anchor: middle;
            pointer-events: none;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .legend {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>网络拓扑可视化</h1>
            <p>物理机和虚拟机网络连接拓扑图</p>
        </div>
        
        <div class="controls">
            <input type="file" id="fileInput" accept=".json" />
            <button onclick="loadSampleData()">加载示例数据</button>
            <button onclick="refreshTopology()">刷新拓扑</button>
            <button onclick="resetZoom()">重置缩放</button>
        </div>
        
        <div class="topology-container">
            <svg id="topology-svg" width="100%" height="100%"></svg>
        </div>
        
        <div class="info-panel">
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4CAF50;"></div>
                    <span>物理机</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #2196F3;"></div>
                    <span>虚拟机</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #FF5722;"></div>
                    <span>TCP连接</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #9C27B0;"></div>
                    <span>UDP连接</span>
                </div>
            </div>
            <div id="topology-info">
                <p>请加载拓扑数据文件或使用示例数据</p>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip" style="display: none;"></div>

    <script>
        let topologyData = null;
        let simulation = null;
        
        // 初始化SVG
        const svg = d3.select("#topology-svg");
        const width = 1160;
        const height = 600;
        
        svg.attr("viewBox", [0, 0, width, height]);
        
        // 创建缩放行为
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on("zoom", (event) => {
                g.attr("transform", event.transform);
            });
        
        svg.call(zoom);
        
        // 创建主要的g元素
        const g = svg.append("g");
        
        // 文件输入处理
        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);
                        loadTopologyData(data);
                    } catch (error) {
                        alert('文件格式错误: ' + error.message);
                    }
                };
                reader.readAsText(file);
            }
        });
        
        function loadSampleData() {
            // 示例数据
            const sampleData = {
                "nodes": [
                    {
                        "node_id": "host1",
                        "node_type": "physical",
                        "name": "物理主机1",
                        "ip_addresses": ["*************", "********"]
                    },
                    {
                        "node_id": "vm1",
                        "node_type": "virtual",
                        "name": "虚拟机1",
                        "ip_addresses": ["*************"]
                    },
                    {
                        "node_id": "vm2",
                        "node_type": "virtual",
                        "name": "虚拟机2",
                        "ip_addresses": ["*************"]
                    }
                ],
                "connections": [
                    {
                        "source_node": "vm1",
                        "target_node": "host1",
                        "protocol": "tcp",
                        "source_ip": "*************",
                        "source_port": 22,
                        "target_ip": "*************",
                        "target_port": 80,
                        "state": "ESTABLISHED"
                    },
                    {
                        "source_node": "vm2",
                        "target_node": "vm1",
                        "protocol": "udp",
                        "source_ip": "*************",
                        "source_port": 53,
                        "target_ip": "*************",
                        "target_port": 53,
                        "state": "ESTABLISHED"
                    }
                ],
                "metadata": {
                    "generated_at": new Date().toISOString(),
                    "hypervisor": "示例主机",
                    "total_nodes": 3,
                    "total_connections": 2
                }
            };
            
            loadTopologyData(sampleData);
        }
        
        function loadTopologyData(data) {
            topologyData = data;
            renderTopology();
            updateInfoPanel();
        }
        
        function renderTopology() {
            if (!topologyData) return;
            
            // 清除现有内容
            g.selectAll("*").remove();
            
            // 准备数据
            const nodes = topologyData.nodes.map(d => ({...d}));
            const links = topologyData.connections.map(d => ({
                source: d.source_node,
                target: d.target_node,
                ...d
            }));
            
            // 创建力导向图模拟
            simulation = d3.forceSimulation(nodes)
                .force("link", d3.forceLink(links).id(d => d.node_id).distance(100))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(width / 2, height / 2));
            
            // 创建连接线
            const link = g.append("g")
                .selectAll("line")
                .data(links)
                .join("line")
                .attr("class", d => `link ${d.protocol}`)
                .attr("stroke-width", 2);
            
            // 创建节点
            const node = g.append("g")
                .selectAll("circle")
                .data(nodes)
                .join("circle")
                .attr("class", d => `node ${d.node_type}`)
                .attr("r", 20)
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended));
            
            // 创建节点标签
            const label = g.append("g")
                .selectAll("text")
                .data(nodes)
                .join("text")
                .attr("class", "node-label")
                .text(d => d.name)
                .attr("dy", 35);
            
            // 添加工具提示
            node.on("mouseover", function(event, d) {
                showTooltip(event, d);
            }).on("mouseout", function() {
                hideTooltip();
            });
            
            link.on("mouseover", function(event, d) {
                showLinkTooltip(event, d);
            }).on("mouseout", function() {
                hideTooltip();
            });
            
            // 更新位置
            simulation.on("tick", () => {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                
                label
                    .attr("x", d => d.x)
                    .attr("y", d => d.y);
            });
        }
        
        function showTooltip(event, d) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${d.name}</strong><br>
                类型: ${d.node_type === 'physical' ? '物理机' : '虚拟机'}<br>
                ID: ${d.node_id}<br>
                IP地址: ${d.ip_addresses.join(', ')}
            `;
            tooltip.style.display = 'block';
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY + 10) + 'px';
        }
        
        function showLinkTooltip(event, d) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>连接信息</strong><br>
                协议: ${d.protocol.toUpperCase()}<br>
                源: ${d.source_ip}:${d.source_port}<br>
                目标: ${d.target_ip}:${d.target_port}<br>
                状态: ${d.state}
            `;
            tooltip.style.display = 'block';
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY + 10) + 'px';
        }
        
        function hideTooltip() {
            document.getElementById('tooltip').style.display = 'none';
        }
        
        function updateInfoPanel() {
            if (!topologyData) return;
            
            const info = document.getElementById('topology-info');
            info.innerHTML = `
                <h3>拓扑信息</h3>
                <p><strong>生成时间:</strong> ${topologyData.metadata.generated_at}</p>
                <p><strong>宿主机:</strong> ${topologyData.metadata.hypervisor}</p>
                <p><strong>节点总数:</strong> ${topologyData.metadata.total_nodes}</p>
                <p><strong>连接总数:</strong> ${topologyData.metadata.total_connections}</p>
            `;
        }
        
        function refreshTopology() {
            if (topologyData) {
                renderTopology();
            }
        }
        
        function resetZoom() {
            svg.transition().duration(750).call(
                zoom.transform,
                d3.zoomIdentity
            );
        }
        
        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
    </script>
</body>
</html>
