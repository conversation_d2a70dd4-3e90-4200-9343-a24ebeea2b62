{"port": 9192, "debug": true, "log_level": "DEBUG", "log_format": "%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s", "enable_host_monitoring": false, "enable_vm_monitoring": true, "collection_interval": 60, "supported_hypervisors": ["kvm", "qemu", "vmware", "hyperv"], "libvirt_uri": "qemu:///system", "include_loopback": false, "include_ipv6": true, "port_range_filter": null, "enable_topology_generation": false, "topology_output_path": "/tmp/network_topology.json", "qga_settings": {"enable_qga": true, "qga_timeout": 30, "fallback_to_nsenter": true, "retry_attempts": 3, "qga_ping_timeout": 10}}