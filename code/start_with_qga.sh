#!/bin/bash

# 启动网络监控系统，使用QGA模式收集虚拟机数据
# <AUTHOR> <EMAIL>

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}网络端口监控系统 - QGA模式${NC}"
echo "=================================================="

# 检查QGA配置文件
CONFIG_FILE="config_qga.json"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${YELLOW}QGA配置文件不存在，创建默认配置...${NC}"
    cat > "$CONFIG_FILE" << 'EOF'
{
  "port": 9192,
  "debug": true,
  "log_level": "INFO",
  "log_format": "%(asctime)s - %(levelname)s - %(message)s",
  "enable_host_monitoring": true,
  "enable_vm_monitoring": true,
  "collection_interval": 30,
  "supported_hypervisors": [
    "kvm",
    "qemu",
    "vmware",
    "hyperv"
  ],
  "libvirt_uri": "qemu:///system",
  "include_loopback": false,
  "include_ipv6": true,
  "port_range_filter": null,
  "enable_topology_generation": true,
  "topology_output_path": "/tmp/network_topology.json",
  "qga_settings": {
    "enable_qga": true,
    "qga_timeout": 30,
    "fallback_to_nsenter": true,
    "retry_attempts": 3,
    "qga_ping_timeout": 5
  }
}
EOF
    echo -e "${GREEN}QGA配置文件已创建: $CONFIG_FILE${NC}"
fi

# 验证配置
INTERVAL=$(grep -o '"collection_interval": [0-9]*' "$CONFIG_FILE" | grep -o '[0-9]*')
QGA_ENABLED=$(grep -o '"enable_qga": [a-z]*' "$CONFIG_FILE" | grep -o '[a-z]*')
FALLBACK_ENABLED=$(grep -o '"fallback_to_nsenter": [a-z]*' "$CONFIG_FILE" | grep -o '[a-z]*')
TOPOLOGY_PATH=$(grep -o '"topology_output_path": "[^"]*"' "$CONFIG_FILE" | sed 's/"topology_output_path": "\(.*\)"/\1/')

echo "当前配置:"
echo "  - 收集间隔: ${INTERVAL}秒"
echo "  - QGA启用: $QGA_ENABLED"
echo "  - 回退到nsenter: $FALLBACK_ENABLED"
echo "  - 输出路径: $TOPOLOGY_PATH"
echo

# 检查系统要求
echo -e "${BLUE}检查系统要求...${NC}"

# 检查libvirt
if ! systemctl is-active --quiet libvirtd; then
    echo -e "${RED}libvirtd服务未运行${NC}"
    exit 1
fi

# 检查virsh命令
if ! command -v virsh &> /dev/null; then
    echo -e "${RED}virsh命令未找到${NC}"
    exit 1
fi

# 获取运行中的虚拟机
VMS=$(virsh list --state-running --name | grep -v '^$' || true)
if [[ -z "$VMS" ]]; then
    echo -e "${YELLOW}警告: 没有找到运行中的虚拟机${NC}"
else
    echo "找到运行中的虚拟机:"
    echo "$VMS" | while read vm; do
        if [[ -n "$vm" ]]; then
            echo "  - $vm"
        fi
    done
fi

echo

# 测试QGA连接
if [[ -n "$VMS" ]]; then
    echo -e "${BLUE}测试QGA连接...${NC}"
    echo "$VMS" | while read vm; do
        if [[ -n "$vm" ]]; then
            echo -n "  测试 $vm: "
            if timeout 5 virsh qemu-agent-command "$vm" '{"execute":"guest-ping"}' >/dev/null 2>&1; then
                echo -e "${GREEN}QGA可用${NC}"
            else
                echo -e "${YELLOW}QGA不可用${NC}"
            fi
        fi
    done
    echo
fi

# 创建输出目录
OUTPUT_DIR=$(dirname "$TOPOLOGY_PATH")
mkdir -p "$OUTPUT_DIR"

# 清理旧的拓扑文件
if [[ -f "$TOPOLOGY_PATH" ]]; then
    echo "清理旧的拓扑文件: $TOPOLOGY_PATH"
    rm -f "$TOPOLOGY_PATH"
fi

echo
echo -e "${BLUE}启动监控系统（QGA模式）...${NC}"
echo "拓扑数据将每30秒生成一次"
echo "QGA优先，回退到nsenter（如果启用）"
echo "按 Ctrl+C 停止"
echo

# 在后台启动QGA测试脚本
if [[ -f "test_qga.py" ]]; then
    echo "可以运行以下命令测试QGA功能:"
    echo "  python3 test_qga.py"
    echo
fi

# 启动主程序
echo "启动主程序..."
python3 app.py --config "$CONFIG_FILE" --debug

echo "程序已退出"
