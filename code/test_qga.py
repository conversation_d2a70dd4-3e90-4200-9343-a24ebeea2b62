#!/usr/bin/env python3
"""
QGA (QEMU Guest Agent) 测试脚本
用于测试虚拟机QGA功能和网络数据收集
"""

import json
import subprocess
import sys
import time
from typing import List, Dict, Any

def run_command(command: List[str], timeout: int = 10) -> List[str]:
    """执行命令并返回输出"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=timeout)
        return result.stdout.strip().splitlines()
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {' '.join(command)}")
        print(f"错误: {e.stderr}")
        return []
    except subprocess.TimeoutExpired:
        print(f"命令超时: {' '.join(command)}")
        return []
    except FileNotFoundError:
        print(f"命令未找到: {command[0]}")
        return []

def get_vm_list() -> List[str]:
    """获取运行中的虚拟机列表"""
    print("获取运行中的虚拟机列表...")
    result = run_command(['virsh', 'list', '--state-running', '--name'])
    vms = [vm.strip() for vm in result if vm.strip()]
    print(f"找到 {len(vms)} 个运行中的虚拟机: {vms}")
    return vms

def test_qga_ping(vm_name: str) -> bool:
    """测试QGA ping命令"""
    print(f"\n测试 {vm_name} 的QGA连接...")
    
    ping_cmd = {"execute": "guest-ping"}
    cmd_json = json.dumps(ping_cmd)
    
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=5)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response:
                print(f"✓ {vm_name}: QGA连接正常")
                return True
            elif 'error' in response:
                print(f"✗ {vm_name}: QGA错误 - {response['error']}")
                return False
        except json.JSONDecodeError as e:
            print(f"✗ {vm_name}: 响应解析失败 - {e}")
    
    print(f"✗ {vm_name}: QGA不可用")
    return False

def get_vm_info(vm_name: str) -> Dict[str, Any]:
    """获取虚拟机基本信息"""
    print(f"\n获取 {vm_name} 的基本信息...")
    
    info_cmd = {"execute": "guest-info"}
    cmd_json = json.dumps(info_cmd)
    
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=10)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response:
                info = response['return']
                print(f"  版本: {info.get('version', 'Unknown')}")
                
                supported_commands = info.get('supported_commands', [])
                print(f"  支持的命令数: {len(supported_commands)}")
                
                # 检查是否支持网络相关命令
                network_commands = [cmd for cmd in supported_commands 
                                  if 'network' in cmd.get('name', '').lower() or 
                                     'exec' in cmd.get('name', '').lower()]
                print(f"  网络相关命令: {[cmd['name'] for cmd in network_commands]}")
                
                return info
        except json.JSONDecodeError as e:
            print(f"  信息解析失败: {e}")
    
    return {}

def test_linux_network_collection(vm_name: str) -> List[str]:
    """测试Linux虚拟机网络数据收集"""
    print(f"\n测试 {vm_name} 的Linux网络数据收集...")
    
    # 执行ss命令
    ss_command = "ss -tuna --no-header"
    exec_cmd = {
        "execute": "guest-exec",
        "arguments": {
            "path": "/bin/bash",
            "arg": ["-c", ss_command],
            "capture-output": True
        }
    }
    
    cmd_json = json.dumps(exec_cmd)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=10)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response and 'pid' in response['return']:
                pid = response['return']['pid']
                print(f"  命令执行PID: {pid}")
                
                # 获取执行结果
                output = get_exec_output(vm_name, pid)
                if output:
                    print(f"  网络连接数据:")
                    for line in output[:5]:  # 只显示前5行
                        print(f"    {line}")
                    if len(output) > 5:
                        print(f"    ... 还有 {len(output) - 5} 行")
                    return output
                else:
                    print("  未获取到网络连接数据")
            else:
                print(f"  命令执行失败: {response}")
        except json.JSONDecodeError as e:
            print(f"  响应解析失败: {e}")
    
    return []

def test_windows_network_collection(vm_name: str) -> List[str]:
    """测试Windows虚拟机网络数据收集"""
    print(f"\n测试 {vm_name} 的Windows网络数据收集...")
    
    # 执行PowerShell命令获取TCP连接
    tcp_command = "Get-NetTCPConnection | Select-Object LocalAddress,LocalPort,RemoteAddress,RemotePort,State | ConvertTo-Json -Compress"
    exec_cmd = {
        "execute": "guest-exec",
        "arguments": {
            "path": "powershell.exe",
            "arg": ["-Command", tcp_command],
            "capture-output": True
        }
    }
    
    cmd_json = json.dumps(exec_cmd)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=15)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response and 'pid' in response['return']:
                pid = response['return']['pid']
                print(f"  命令执行PID: {pid}")
                
                # 获取执行结果
                output = get_exec_output(vm_name, pid)
                if output:
                    try:
                        # 尝试解析JSON输出
                        json_data = json.loads('\n'.join(output))
                        if isinstance(json_data, list):
                            print(f"  找到 {len(json_data)} 个TCP连接")
                            for conn in json_data[:3]:  # 显示前3个连接
                                print(f"    {conn.get('LocalAddress')}:{conn.get('LocalPort')} -> "
                                      f"{conn.get('RemoteAddress')}:{conn.get('RemotePort')} ({conn.get('State')})")
                        else:
                            print(f"  单个连接: {json_data}")
                        return output
                    except json.JSONDecodeError:
                        print(f"  原始输出:")
                        for line in output[:5]:
                            print(f"    {line}")
                        return output
                else:
                    print("  未获取到网络连接数据")
            else:
                print(f"  命令执行失败: {response}")
        except json.JSONDecodeError as e:
            print(f"  响应解析失败: {e}")
    
    return []

def get_exec_output(vm_name: str, pid: int) -> List[str]:
    """获取QGA命令执行输出"""
    status_cmd = {
        "execute": "guest-exec-status",
        "arguments": {"pid": pid}
    }
    
    # 等待命令执行完成
    max_wait = 10
    wait_count = 0
    
    while wait_count < max_wait:
        cmd_json = json.dumps(status_cmd)
        virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
        result = run_command(virsh_cmd, timeout=5)
        
        if result:
            try:
                response = json.loads('\n'.join(result))
                if response.get('return', {}).get('exited', False):
                    if 'out-data' in response['return']:
                        import base64
                        output_data = base64.b64decode(response['return']['out-data']).decode('utf-8')
                        return output_data.strip().split('\n') if output_data.strip() else []
                    else:
                        return []
            except json.JSONDecodeError:
                pass
        
        time.sleep(1)
        wait_count += 1
    
    print(f"  命令执行超时 (PID: {pid})")
    return []

def detect_vm_os(vm_name: str) -> str:
    """检测虚拟机操作系统类型"""
    print(f"\n检测 {vm_name} 的操作系统类型...")
    
    # 尝试执行Linux命令
    linux_test = {
        "execute": "guest-exec",
        "arguments": {
            "path": "/bin/uname",
            "arg": ["-s"],
            "capture-output": True
        }
    }
    
    cmd_json = json.dumps(linux_test)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=5)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response and 'pid' in response['return']:
                pid = response['return']['pid']
                output = get_exec_output(vm_name, pid)
                if output and 'Linux' in '\n'.join(output):
                    print(f"  检测到Linux系统")
                    return 'linux'
        except:
            pass
    
    # 尝试执行Windows命令
    windows_test = {
        "execute": "guest-exec",
        "arguments": {
            "path": "cmd.exe",
            "arg": ["/c", "echo Windows"],
            "capture-output": True
        }
    }
    
    cmd_json = json.dumps(windows_test)
    virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
    result = run_command(virsh_cmd, timeout=5)
    
    if result:
        try:
            response = json.loads('\n'.join(result))
            if 'return' in response and 'pid' in response['return']:
                pid = response['return']['pid']
                output = get_exec_output(vm_name, pid)
                if output and 'Windows' in '\n'.join(output):
                    print(f"  检测到Windows系统")
                    return 'windows'
        except:
            pass
    
    print(f"  无法确定操作系统类型")
    return 'unknown'

def main():
    """主函数"""
    print("=" * 60)
    print("QGA (QEMU Guest Agent) 功能测试")
    print("=" * 60)
    
    # 获取虚拟机列表
    vms = get_vm_list()
    
    if not vms:
        print("没有找到运行中的虚拟机")
        return
    
    # 测试每个虚拟机
    for vm_name in vms:
        print(f"\n{'='*40}")
        print(f"测试虚拟机: {vm_name}")
        print(f"{'='*40}")
        
        # 测试QGA连接
        if not test_qga_ping(vm_name):
            print(f"跳过 {vm_name}（QGA不可用）")
            continue
        
        # 获取虚拟机信息
        vm_info = get_vm_info(vm_name)
        
        # 检测操作系统类型
        os_type = detect_vm_os(vm_name)
        
        # 根据操作系统类型测试网络数据收集
        if os_type == 'linux':
            test_linux_network_collection(vm_name)
        elif os_type == 'windows':
            test_windows_network_collection(vm_name)
        else:
            print(f"  跳过网络测试（未知操作系统）")
    
    print(f"\n{'='*60}")
    print("测试完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
