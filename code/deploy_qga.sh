#!/bin/bash

# QGA模式网络端口监控系统部署脚本
# <AUTHOR> <EMAIL>
# 版本: v2.0.0-qga

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
IMAGE_NAME="network_exporter_qga"
IMAGE_TAG="v2.0.0"
CONTAINER_NAME="network_exporter_qga"
CONFIG_DIR="/etc/network_exporter"
LOG_DIR="/var/log/network_exporter"
DATA_DIR="/var/lib/network_exporter"

# 函数定义
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查QGA模式系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查libvirt
    if ! systemctl is-active --quiet libvirtd; then
        log_warn "libvirtd服务未运行，尝试启动..."
        sudo systemctl start libvirtd || {
            log_error "无法启动libvirtd服务"
            exit 1
        }
    fi
    
    # 检查virsh命令
    if ! command -v virsh &> /dev/null; then
        log_error "virsh命令未找到，请安装libvirt-clients"
        exit 1
    fi
    
    # 检查运行中的虚拟机
    VMS=$(virsh list --state-running --name | grep -v '^$' || true)
    if [[ -z "$VMS" ]]; then
        log_warn "没有找到运行中的虚拟机"
    else
        log_info "找到运行中的虚拟机:"
        echo "$VMS" | while read vm; do
            if [[ -n "$vm" ]]; then
                echo "  - $vm"
            fi
        done
    fi
    
    log_info "系统要求检查完成"
}

# 测试QGA连接
test_qga_connections() {
    log_info "测试虚拟机QGA连接..."
    
    VMS=$(virsh list --state-running --name | grep -v '^$' || true)
    if [[ -z "$VMS" ]]; then
        log_warn "没有虚拟机可测试"
        return
    fi
    
    local qga_available=false
    echo "$VMS" | while read vm; do
        if [[ -n "$vm" ]]; then
            echo -n "  测试 $vm: "
            if timeout 5 virsh qemu-agent-command "$vm" '{"execute":"guest-ping"}' >/dev/null 2>&1; then
                echo -e "${GREEN}QGA可用${NC}"
                qga_available=true
            else
                echo -e "${YELLOW}QGA不可用${NC}"
            fi
        fi
    done
    
    if [[ "$qga_available" == "false" ]]; then
        log_warn "所有虚拟机的QGA都不可用，请确保虚拟机内已安装并启动qemu-guest-agent"
        echo "Linux虚拟机安装命令:"
        echo "  sudo apt-get install qemu-guest-agent"
        echo "  sudo systemctl enable qemu-guest-agent"
        echo "  sudo systemctl start qemu-guest-agent"
        echo
        echo "Windows虚拟机:"
        echo "  下载并安装 QEMU Guest Agent for Windows"
    fi
}

# 构建Docker镜像
build_image() {
    log_info "构建QGA模式Docker镜像..."
    
    # 检查Dockerfile是否存在
    if [[ ! -f "Dockerfile.qga" ]]; then
        log_error "Dockerfile.qga不存在"
        exit 1
    fi
    
    # 构建镜像
    log_info "开始构建镜像 $IMAGE_NAME:$IMAGE_TAG"
    if docker build -f Dockerfile.qga -t "$IMAGE_NAME:$IMAGE_TAG" . --no-cache; then
        docker tag "$IMAGE_NAME:$IMAGE_TAG" "$IMAGE_NAME:latest"
        log_info "Docker镜像构建完成"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    sudo mkdir -p "$CONFIG_DIR"
    sudo mkdir -p "$LOG_DIR"
    sudo mkdir -p "$DATA_DIR"
    
    # 设置权限
    sudo chmod 755 "$CONFIG_DIR"
    sudo chmod 755 "$LOG_DIR"
    sudo chmod 755 "$DATA_DIR"
    
    log_info "目录创建完成"
}

# 生成配置文件
generate_config() {
    log_info "生成QGA配置文件..."
    
    if [[ ! -f "$CONFIG_DIR/config.json" ]]; then
        sudo cp config_qga.json "$CONFIG_DIR/config.json"
        log_info "QGA配置文件已复制到 $CONFIG_DIR/config.json"
    else
        log_warn "配置文件已存在，跳过生成"
    fi
    
    # 更新配置文件中的路径
    sudo sed -i "s|/tmp/network_topology.json|$DATA_DIR/network_topology.json|g" "$CONFIG_DIR/config.json"
}

# 停止现有容器
stop_container() {
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "停止现有容器..."
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
    fi
}

# 启动容器
start_container() {
    log_info "启动QGA模式容器..."
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        --privileged \
        --network host \
        -v /var/run/libvirt:/var/run/libvirt:ro \
        -v /var/run:/var/run:ro \
        -v /proc:/host_proc:ro \
        -v "$CONFIG_DIR:/etc/network_exporter:ro" \
        -v "$LOG_DIR:/var/log/network_exporter" \
        -v "$DATA_DIR:/var/lib/network_exporter" \
        "$IMAGE_NAME:$IMAGE_TAG"
    
    log_info "容器启动完成"
}

# 检查服务状态
check_service() {
    log_info "检查服务状态..."
    
    sleep 5
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "容器运行正常"
        
        # 检查端口
        local port=$(grep -o '"port": [0-9]*' "$CONFIG_DIR/config.json" | grep -o '[0-9]*')
        if curl -s "http://localhost:$port/metrics" > /dev/null; then
            log_info "服务端点正常响应"
        else
            log_warn "服务端点无响应，请检查日志"
        fi
    else
        log_error "容器启动失败"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
}

# 显示使用信息
show_usage() {
    log_info "QGA模式部署完成！"
    echo
    echo "服务信息:"
    echo "  - 容器名称: $CONTAINER_NAME"
    echo "  - 配置文件: $CONFIG_DIR/config.json"
    echo "  - 日志目录: $LOG_DIR"
    echo "  - 数据目录: $DATA_DIR"
    echo "  - QGA模式: 启用"
    echo
    echo "常用命令:"
    echo "  - 查看日志: docker logs $CONTAINER_NAME"
    echo "  - 重启服务: docker restart $CONTAINER_NAME"
    echo "  - 停止服务: docker stop $CONTAINER_NAME"
    echo "  - 查看指标: curl http://localhost:9192/metrics"
    echo "  - 查看拓扑: cat $DATA_DIR/network_topology.json"
    echo "  - 测试QGA: python test_qga.py"
    echo
    echo "QGA故障排除:"
    echo "  - 确保虚拟机内已安装qemu-guest-agent"
    echo "  - 检查虚拟机QGA服务状态"
    echo "  - 查看容器日志了解详细错误信息"
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    stop_container
    docker rmi "$IMAGE_NAME:$IMAGE_TAG" "$IMAGE_NAME:latest" 2>/dev/null || true
    sudo rm -rf "$CONFIG_DIR" "$LOG_DIR" "$DATA_DIR"
    log_info "清理完成"
}

# 主函数
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "开始部署QGA模式网络端口监控系统..."
            check_requirements
            test_qga_connections
            create_directories
            generate_config
            build_image
            stop_container
            start_container
            check_service
            show_usage
            ;;
        "start")
            log_info "启动服务..."
            start_container
            check_service
            ;;
        "stop")
            log_info "停止服务..."
            stop_container
            ;;
        "restart")
            log_info "重启服务..."
            stop_container
            start_container
            check_service
            ;;
        "status")
            log_info "检查服务状态..."
            check_service
            ;;
        "logs")
            docker logs -f "$CONTAINER_NAME"
            ;;
        "test-qga")
            test_qga_connections
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo
            echo "命令:"
            echo "  deploy   - 完整部署（默认）"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  status   - 检查状态"
            echo "  logs     - 查看日志"
            echo "  test-qga - 测试QGA连接"
            echo "  cleanup  - 清理所有资源"
            echo "  help     - 显示帮助"
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
