#!/usr/bin/env python3

import argparse
import json
import logging
import os
import platform
import re
import subprocess
import time
import socket
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import ipaddress

import libvirt
import psutil
from prometheus_client import start_http_server, Gauge, REGISTRY
from prometheus_client.core import GaugeMetricFamily, Metric

# --- Configuration ---
@dataclass
class Config:
    """系统配置类"""
    # 服务配置
    port: int = 9192
    debug: bool = False
    log_level: str = "INFO"
    log_format: str = '%(asctime)s - %(levelname)s - %(message)s'

    # 监控配置
    enable_host_monitoring: bool = True
    enable_vm_monitoring: bool = True
    collection_interval: int = 30

    # 虚拟化平台配置
    libvirt_uri: str = "qemu:///system"

    # 网络配置
    include_loopback: bool = False
    include_ipv6: bool = True
    port_range_filter: Optional[Tuple[int, int]] = None

    # QGA配置
    qga_settings: Dict[str, Any] = None

    def __post_init__(self):
        if self.qga_settings is None:
            self.qga_settings = {
                "enable_qga": True,
                "qga_timeout": 30,
                "retry_attempts": 3,
                "qga_ping_timeout": 5
            }

    @classmethod
    def from_file(cls, config_path: str) -> 'Config':
        """从配置文件加载配置"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        return cls()

    def to_file(self, config_path: str):
        """保存配置到文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(self), f, indent=2, ensure_ascii=False)

# 全局配置实例
config = Config()

# --- Data Structures ---
@dataclass
class NetworkConnection:
    """网络连接信息"""
    protocol: str
    local_ip: str
    local_port: int
    remote_ip: str = ""
    remote_port: int = 0
    state: str = ""
    connection_type: str = "connection"  # "connection" or "listener"

@dataclass
class VMInfo:
    """虚拟机信息"""
    name: str
    id: str
    hypervisor: str
    os_type: str = "unknown"
    state: str = "unknown"
    pid: Optional[int] = None

# --- Helper Functions ---
def get_current_hostname():
    """Gets the FQDN of the current host."""
    return socket.getfqdn()

def run_command(command, timeout=10):
    """Executes a shell command and returns its output."""
    try:
        logging.debug(f"Running command: {' '.join(command)}")
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=timeout)
        return result.stdout.strip().splitlines()
    except subprocess.CalledProcessError as e:
        logging.error(f"Command '{' '.join(command)}' failed with error: {e.stderr}")
    except subprocess.TimeoutExpired:
        logging.error(f"Command '{' '.join(command)}' timed out after {timeout} seconds.")
    except FileNotFoundError:
        logging.error(f"Command not found: {command[0]}. Ensure it is installed and in PATH.")
    return []

def is_illegal_ip(ip_string: str) -> bool:
    """
    检查一个字符串是否是非法的ip地址。
    非法的规则: 
        1. 符合规则 IPv4 映射的 IPv6 地址，例如 '::ffff:***********'
        2. 不是有效的ip地址

    参数:
        ip_string (str): 需要检查的 IP 地址字符串。

    返回:
        bool: 如果是 IPv4 映射的 IPv6 地址，则返回 True,
              否则返回 False。对于无效的 IP 地址格式, 也返回 True.
    """
    # 首先，检查输入是否为字符串类型，如果不是则直接返回 False
    if not isinstance(ip_string, str):
        return True

    try:
        # 将字符串解析成一个 IP 地址对象
        addr = ipaddress.ip_address(ip_string)

        # 检查两个条件：
        # 1. 地址对象必须是 IPv6Address 的实例。
        # 2. 该对象的 .ipv4_mapped 属性必须为 True。
        # 两个条件同时满足，才符合规则。
        return isinstance(addr, ipaddress.IPv6Address) and addr.ipv4_mapped

    except ValueError:
        # 如果 ip_string 根本不是一个有效的 IP 地址格式（例如 "hello" 或 ""），
        # ipaddress.ip_address() 会抛出 ValueError 异常。
        # 这种情况下，它显然符合非法规则，所以我们捕获异常并返回 True。
        return True

# --- Abstract Base Classes ---
class HypervisorAdapter(ABC):
    """虚拟化平台适配器抽象基类"""

    def __init__(self, hypervisor_hostname: str):
        self.hypervisor_hostname = hypervisor_hostname
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    def get_vms(self) -> List[VMInfo]:
        """获取虚拟机列表"""
        pass

    @abstractmethod
    def get_vm_connections(self, vm_info: VMInfo) -> List[NetworkConnection]:
        """获取虚拟机的网络连接"""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """检查适配器是否可用"""
        pass

class LibvirtAdapter(HypervisorAdapter):
    """Libvirt适配器，支持KVM/QEMU"""

    def __init__(self, hypervisor_hostname: str, uri: str = "qemu:///system"):
        super().__init__(hypervisor_hostname)
        self.uri = uri
        self.conn = None
        self._connect()

    def _connect(self):
        """连接到libvirt"""
        try:
            self.logger.debug(f"Attempting to connect to libvirt URI: {self.uri}")
            self.conn = libvirt.openReadOnly(self.uri)
            if self.conn is None:
                self.logger.error(f'Failed to open libvirt connection to {self.uri}')
            else:
                self.logger.info(f"Libvirt connected successfully to {self.uri}")
                # 测试连接
                try:
                    hostname = self.conn.getHostname()
                    self.logger.debug(f"Connected to libvirt host: {hostname}")
                except Exception as e:
                    self.logger.warning(f"Libvirt connection test failed: {e}")
        except libvirt.libvirtError as e:
            self.logger.error(f"Libvirt connection error: {e}")
            self.conn = None
        except Exception as e:
            self.logger.error(f"Unexpected error connecting to libvirt: {e}")
            self.conn = None

    def is_available(self) -> bool:
        return self.conn is not None

    def get_vms(self) -> List[VMInfo]:
        """获取活跃的虚拟机列表"""
        vms = []
        if not self.conn:
            return vms

        try:
            domains = self.conn.listAllDomains(libvirt.VIR_CONNECT_LIST_DOMAINS_ACTIVE)
            for domain in domains:
                vm_info = VMInfo(
                    name=domain.name(),
                    id=domain.UUIDString(),
                    hypervisor="kvm",
                    state="running",
                    pid=self._get_vm_pid(domain)
                )
                # 尝试检测操作系统类型
                vm_info.os_type = self._detect_vm_os(domain)
                self.logger.info(f"VM {vm_info.name} os_type = {vm_info.os_type}")
                vms.append(vm_info)
        except libvirt.libvirtError as e:
            self.logger.error(f"Failed to get VM list: {e}")

        return vms

    def _get_vm_pid(self, domain) -> Optional[int]:
        """获取虚拟机进程PID"""
        pid_path = f"/var/run/libvirt/qemu/{domain.name()}.pid"
        try:
            with open(pid_path, 'r') as f:
                pid = int(f.read().strip())
                return pid
        except (FileNotFoundError, ValueError) as e:
            self.logger.debug(f"Failed to get PID for {domain.name()}: {e}")
            return None

    def _detect_vm_os(self, domain) -> str:
        """检测虚拟机操作系统类型"""
        vm_name = domain.name()
        self.logger.debug(f"Detecting OS type for VM: {vm_name}")

        os_type = self._detect_os_via_qga(vm_name)
        if os_type != 'unknown':
            self.logger.info(f"VM {vm_name} OS detected via QGA: {os_type}")
            return os_type

        self.logger.warning(f"Unable to detect OS type for VM {vm_name}, defaulting to 'unknown'")
        return 'unknown'

    def _detect_os_via_qga(self, vm_name: str) -> str:
        """通过QGA检测操作系统类型"""
        try:
            # 检查QGA是否可用
            if not self._is_qga_available(vm_name):
                self.logger.debug(f"QGA not available for {vm_name}, skipping QGA OS detection")
                return 'unknown'

            # 尝试执行Linux命令
            linux_result = self._test_linux_command(vm_name)
            if linux_result:
                return 'linux'

            # 尝试执行Windows命令
            windows_result = self._test_windows_command(vm_name)
            if windows_result:
                return 'windows'

            return 'unknown'

        except Exception as e:
            self.logger.debug(f"QGA OS detection failed for {vm_name}: {e}")
            return 'unknown'

    def _test_linux_command(self, vm_name: str) -> bool:
        """测试Linux命令执行"""
        try:
            # 执行uname命令
            exec_cmd = {
                "execute": "guest-exec",
                "arguments": {
                    "path": "/bin/uname",
                    "arg": ["-s"],
                    "capture-output": True
                }
            }

            cmd_json = json.dumps(exec_cmd)
            virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
            result = run_command(virsh_cmd, timeout=10)

            if result:
                response = json.loads('\n'.join(result))
                if 'return' in response and 'pid' in response['return']:
                    pid = response['return']['pid']
                    output = self._get_qga_exec_status(pid, vm_name)

                    if output and 'out-data' in output:
                        import base64
                        output_data = base64.b64decode(output['out-data']).decode('utf-8')
                        if 'Linux' in output_data:
                            self.logger.debug(f"Linux detected via uname for {vm_name}")
                            return True

            return False

        except Exception as e:
            self.logger.debug(f"Linux command test failed for {vm_name}: {e}")
            return False

    def _test_windows_command(self, vm_name: str) -> bool:
        """测试Windows命令执行"""
        try:
            # 执行Windows ver命令
            exec_cmd = {
                "execute": "guest-exec",
                "arguments": {
                    "path": "cmd.exe",
                    "arg": ["/c", "ver"],
                    "capture-output": True
                }
            }

            cmd_json = json.dumps(exec_cmd)
            virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
            result = run_command(virsh_cmd, timeout=10)

            if result:
                response = json.loads('\n'.join(result))
                if 'return' in response and 'pid' in response['return']:
                    pid = response['return']['pid']
                    output = self._get_qga_exec_status(pid, vm_name)
                    
                    if output and 'out-data' in output:
                        # import base64
                        # output_data = base64.b64decode(output['out-data']).decode('utf-8')
                        # if 'Windows' in output_data or 'Microsoft' in output_data:
                        #     self.logger.debug(f"Windows detected via ver command for {vm_name}")
                        #     return True
                        import base64
                        import chardet
                        decoded_bytes = base64.b64decode(output['out-data'])
                        # 检测编码
                        detection = chardet.detect(decoded_bytes)
                        encoding = detection['encoding']
                        self.logger.debug(f"Detected encoding: {encoding} with confidence {detection['confidence']}")
                        # 使用检测到的编码进行解码
                        if encoding:
                            output_data = decoded_bytes.decode(encoding)
                            self.logger.debug(f"out-data decoded: {output_data}")
                            if 'Windows' in output_data or 'Microsoft' in output_data:
                                self.logger.debug(f"Windows detected via ver command for {vm_name}")
                                return True
                        else:
                            print("Could not detect encoding.")

            return False

        except Exception as e:
            self.logger.debug(f"Windows command test failed for {vm_name}: {e}")
            return False

    def get_vm_connections(self, vm_info: VMInfo) -> List[NetworkConnection]:
        """获取虚拟机的网络连接（优先使用QGA）"""
        connections = []

        # 检查是否启用QGA
        if config.qga_settings.get("enable_qga", True):
            # 首先检查QGA是否可用
            if self._is_qga_available(vm_info.name):
                self.logger.debug(f"QGA available for VM {vm_info.name}, using QGA method")

                # 使用QGA命令获取网络连接信息
                if vm_info.os_type == 'windows':
                    connections = self._get_windows_vm_connections_qga(vm_info)
                else:
                    connections = self._get_linux_vm_connections_qga(vm_info)
            else:
                self.logger.info(f"QGA not available for VM {vm_info.name}")
        else:
            self.logger.info(f"QGA disabled, using nsenter method for VM {vm_info.name}")

            # 直接使用nsenter方法（仅适用于Linux）
            if vm_info.os_type != 'windows' and vm_info.pid:
                connections = self._get_linux_vm_connections_nsenter(vm_info)
            else:
                self.logger.warning(f"Cannot monitor Windows VM {vm_info.name} without QGA")

        return connections

    def _get_linux_vm_connections_qga(self, vm_info: VMInfo) -> List[NetworkConnection]:
        """使用QGA获取Linux虚拟机的网络连接"""
        connections = []
        try:
            self.logger.debug(f"Getting Linux VM connections via QGA for {vm_info.name}")

            # 使用QGA执行ss命令获取网络连接
            ss_command = "ss -tuna --no-header"
            ss_output = self._execute_qga_command(vm_info.name, "guest-exec", {
                "path": "/bin/bash",
                "arg": ["-c", ss_command],
                "capture-output": True
            })

            if ss_output:
                connections = parse_ss_output(ss_output)
                self.logger.debug(f"Found {len(connections)} connections for Linux VM {vm_info.name}")

        except Exception as e:
            self.logger.error(f"Failed to get connections for Linux VM {vm_info.name} via QGA: {e}")
            # 回退到nsenter方法（如果PID可用）
            if vm_info.pid:
                self.logger.info(f"Falling back to nsenter method for {vm_info.name}")
                connections = self._get_linux_vm_connections_nsenter(vm_info)

        return connections

    def _get_windows_vm_connections_qga(self, vm_info: VMInfo) -> List[NetworkConnection]:
        """使用QGA获取Windows虚拟机的网络连接"""
        connections = []
        try:
            self.logger.debug(f"Getting Windows VM connections via QGA for {vm_info.name}")

            # 使用QGA执行PowerShell命令获取网络连接
            # Get-NetTCPConnection 和 Get-NetUDPEndpoint 命令
            # tcp_command = "Get-NetTCPConnection | Select-Object LocalAddress,LocalPort,RemoteAddress,RemotePort,State | ConvertTo-Json -Compress"
            tcp_command = "Get-NetTCPConnection | Select-Object LocalAddress,LocalPort,RemoteAddress,RemotePort,@{Name='State';Expression={$_.State.ToString()}} | ConvertTo-Json -Compress"
            udp_command = "Get-NetUDPEndpoint | Select-Object LocalAddress,LocalPort | ConvertTo-Json -Compress"

            # 获取TCP连接
            tcp_output = self._execute_qga_command(vm_info.name, "guest-exec", {
                "path": "powershell.exe",
                "arg": ["-Command", tcp_command],
                "capture-output": True
            })

            if tcp_output:
                tcp_connections = self._parse_windows_tcp_output(tcp_output)
                connections.extend(tcp_connections)

            # 获取UDP端点
            udp_output = self._execute_qga_command(vm_info.name, "guest-exec", {
                "path": "powershell.exe",
                "arg": ["-Command", udp_command],
                "capture-output": True
            })

            if udp_output:
                udp_connections = self._parse_windows_udp_output(udp_output)
                connections.extend(udp_connections)

            self.logger.debug(f"Found {len(connections)} connections for Windows VM {vm_info.name}")

        except Exception as e:
            self.logger.error(f"Failed to get connections for Windows VM {vm_info.name} via QGA: {e}")

        return connections

    def _get_linux_vm_connections_nsenter(self, vm_info: VMInfo) -> List[NetworkConnection]:
        """使用nsenter获取Linux虚拟机的网络连接（回退方法）"""
        connections = []
        try:
            ss_output = run_command([
                'nsenter', '-t', str(vm_info.pid), '-n',
                'ss', '-tuna', '--no-header'
            ])
            connections = parse_ss_output(ss_output)
        except Exception as e:
            self.logger.error(f"Failed to get connections for Linux VM {vm_info.name} via nsenter: {e}")

        return connections

    def _is_qga_available(self, vm_name: str) -> bool:
        """检查虚拟机是否支持QGA"""
        try:
            # 尝试执行简单的QGA命令来检查可用性
            ping_cmd = {"execute": "guest-ping"}
            cmd_json = json.dumps(ping_cmd)

            timeout = config.qga_settings.get("qga_ping_timeout", 5)
            virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
            result = run_command(virsh_cmd, timeout=timeout)

            if result:
                response_text = '\n'.join(result)
                response = json.loads(response_text)

                # 检查是否有错误
                if 'error' in response:
                    self.logger.debug(f"QGA error for VM {vm_name}: {response['error']}")
                    return False

                return 'return' in response

            return False

        except json.JSONDecodeError as e:
            self.logger.debug(f"QGA response parse error for VM {vm_name}: {e}")
            return False
        except Exception as e:
            self.logger.debug(f"QGA not available for VM {vm_name}: {e}")
            return False

    def _execute_qga_command(self, vm_name: str, command: str, arguments: dict = None) -> List[str]:
        """执行QGA命令"""
        try:
            # 保存当前虚拟机名称用于后续状态查询
            self._current_vm_name = vm_name

            # 构建virsh qemu-agent-command命令
            if arguments:
                qga_cmd = {
                    "execute": command,
                    "arguments": arguments
                }
            else:
                qga_cmd = {"execute": command}

            cmd_json = json.dumps(qga_cmd)

            # 执行virsh命令
            virsh_cmd = [
                'virsh', 'qemu-agent-command', vm_name, cmd_json
            ]

            self.logger.debug(f"Executing QGA command: {' '.join(virsh_cmd)}")
            result = run_command(virsh_cmd, timeout=30)

            if result:
                # 解析QGA响应
                response_text = '\n'.join(result)
                self.logger.debug(f"QGA response: {response_text}")
                response = json.loads(response_text)
                return self._parse_qga_response(response, command, vm_name)

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse QGA response for {vm_name}: {e}")
        except Exception as e:
            self.logger.error(f"Failed to execute QGA command for {vm_name}: {e}")

        return []

    def _parse_qga_response(self, response: dict, command: str, vm_name: str) -> List[str]:
        """解析QGA响应"""
        try:
            if 'return' not in response:
                self.logger.warning(f"QGA response missing 'return' field: {response}")
                return []

            if command == "guest-exec":
                # 对于guest-exec命令，需要获取执行结果
                pid = response['return'].get('pid')
                if pid:
                    # 获取命令执行状态和输出
                    status_response = self._get_qga_exec_status(pid, vm_name)
                    if status_response and 'out-data' in status_response:
                        # Base64解码输出
                        import base64
                        output_data = base64.b64decode(status_response['out-data']).decode('utf-8')
                        return output_data.strip().split('\n') if output_data.strip() else []

            elif command == "guest-network-get-interfaces":
                # 网络接口信息
                interfaces = response['return']
                return self._format_interface_info(interfaces)

            return []

        except Exception as e:
            self.logger.error(f"Failed to parse QGA response: {e}")
            return []

    def _get_qga_exec_status(self, pid: int, vm_name: str) -> dict:
        """获取QGA命令执行状态"""
        try:
            status_cmd = {
                "execute": "guest-exec-status",
                "arguments": {"pid": pid}
            }

            # 等待命令执行完成
            max_wait = 10  # 最多等待10秒
            wait_count = 0

            while wait_count < max_wait:
                cmd_json = json.dumps(status_cmd)
                virsh_cmd = ['virsh', 'qemu-agent-command', vm_name, cmd_json]
                result = run_command(virsh_cmd, timeout=5)
                self.logger.debug(f"_get_qga_exec_status result = {result}")
                if result:
                    response_text = '\n'.join(result)
                    response = json.loads(response_text)

                    if response.get('return', {}).get('exited', False):
                        return response['return']

                time.sleep(1)
                wait_count += 1

            self.logger.warning(f"QGA command execution timeout for PID {pid} on VM {vm_name}")
            return {}

        except Exception as e:
            self.logger.error(f"Failed to get QGA exec status for VM {vm_name}: {e}")
            return {}

    def _parse_windows_tcp_output(self, output_lines: List[str]) -> List[NetworkConnection]:
        """解析Windows TCP连接输出"""
        connections = []
        try:
            # 合并所有行为一个JSON字符串
            json_str = '\n'.join(output_lines)
            tcp_data = json.loads(json_str)

            self.logger.debug(f"_parse_windows_tcp_output tcp_data = {tcp_data}")
            self.logger.debug(f"_parse_windows_tcp_output tcp_data type = {type(tcp_data)}")

            # 处理单个对象或对象数组
            if isinstance(tcp_data, dict):
                tcp_data = [tcp_data]

            for conn in tcp_data:
                try:
                    conn_state = str(conn.get('State', 'UNKNOWN'))
                    if conn_state != 'Established': continue
                    if is_illegal_ip(str(conn.get('LocalAddress', ''))) or is_illegal_ip(str(conn.get('RemoteAddress', ''))): continue
                    connection = NetworkConnection(
                        protocol='tcp',
                        local_ip=str(conn.get('LocalAddress', '')),
                        local_port=int(conn.get('LocalPort', 0)),
                        remote_ip=str(conn.get('RemoteAddress', '')),
                        remote_port=int(conn.get('RemotePort', 0)),
                        state=conn_state,
                        connection_type='connection'
                    )
                    connections.append(connection)
                except (ValueError, KeyError) as e:
                    self.logger.debug(f"Skipping invalid TCP connection data: {e}")

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse Windows TCP JSON output: {e}")
        except Exception as e:
            self.logger.error(f"Error parsing Windows TCP output: {e}")

        return connections

    def _parse_windows_udp_output(self, output_lines: List[str]) -> List[NetworkConnection]:
        """解析Windows UDP端点输出"""
        connections = []
        try:
            json_str = '\n'.join(output_lines)
            udp_data = json.loads(json_str)

            if isinstance(udp_data, dict):
                udp_data = [udp_data]

            for endpoint in udp_data:
                try:
                    if is_illegal_ip(endpoint.get('LocalAddress', '')): continue
                    connection = NetworkConnection(
                        protocol='udp',
                        local_ip=str(endpoint.get('LocalAddress', '')),
                        local_port=int(endpoint.get('LocalPort', 0)),
                        connection_type='listener'
                    )
                    connections.append(connection)
                except (ValueError, KeyError) as e:
                    self.logger.debug(f"Skipping invalid UDP endpoint data: {e}")

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse Windows UDP JSON output: {e}")
        except Exception as e:
            self.logger.error(f"Error parsing Windows UDP output: {e}")

        return connections

    def _format_interface_info(self, interfaces: List[dict]) -> List[str]:
        """格式化网络接口信息"""
        formatted = []
        for iface in interfaces:
            name = iface.get('name', 'unknown')
            for addr in iface.get('ip-addresses', []):
                ip = addr.get('ip-address', '')
                prefix = addr.get('prefix', 0)
                formatted.append(f"{name}: {ip}/{prefix}")
        return formatted

    def __del__(self):
        if self.conn:
            self.conn.close()

def parse_ss_output(output_lines, protocol_filter=None) -> List[NetworkConnection]:
    """
    解析 'ss -tuna' 或 'ss -luna' 的输出
    返回 NetworkConnection 对象列表
    """
    connections = []
    addr_port_re = re.compile(r"(\S+):(\S+)")

    # logging.debug(f"parse_ss_output = {output_lines}")

    for line in output_lines:
        if line.startswith("State") or line.startswith("Netid"):
            continue

        parts = line.strip().split()
        if not parts:
            continue

        logging.debug(f"parse_ss_output part = {parts}")

        # 确定协议类型
        proto = ""
        if "tcp" in parts[0].lower():
            proto = "tcp"
        elif "udp" in parts[0].lower():
            proto = "udp"

        # logging.debug(f"parse_ss_output proto = {proto}")

        if protocol_filter and proto != protocol_filter:
            continue
        if not proto:
            continue

        state = "LISTEN" if parts[1] == "LISTEN" else parts[1]

        # logging.debug(f"parse_ss_output proto = {state}")

        if len(parts) < 5:
            logging.debug(f"Skipping short line: {line}")
            continue

        # 解析本地地址和端口
        local_addr_port_str = parts[4]
        local_match = addr_port_re.match(local_addr_port_str)

        # logging.debug(f"parse_ss_output local_addr_port_str = {local_addr_port_str}")

        if not local_match:
            logging.debug(f"Could not parse local address: {local_addr_port_str}")
            continue

        local_ip, local_port_str = local_match.groups()

        # logging.debug(f"parse_ss_output local_ip = {local_ip}, local_port_str = {local_port_str}")

        try:
            local_port = int(local_port_str)
        except ValueError:
            logging.debug(f"Invalid local port: {local_port_str}")
            continue

        if state == "LISTEN":
            # 监听连接
            if is_illegal_ip(local_ip): continue
            connection = NetworkConnection(
                protocol=proto,
                local_ip=local_ip,
                local_port=local_port,
                connection_type="listener"
            )
            connections.append(connection)
        elif state == "ESTAB":
            # 活跃连接
            peer_addr_port_str = parts[5]
            peer_match = addr_port_re.match(peer_addr_port_str)

            if peer_match:
                remote_ip, remote_port_str = peer_match.groups()
                # logging.debug(f"parse_ss_output remote_ip = {remote_ip}, remote_port_str = {remote_port_str}")
                if is_illegal_ip(local_ip) or is_illegal_ip(remote_ip): continue
                try:
                    remote_port = int(remote_port_str)
                    connection = NetworkConnection(
                        protocol=proto,
                        local_ip=local_ip,
                        local_port=local_port,
                        remote_ip=remote_ip,
                        remote_port=remote_port,
                        state=state,
                        connection_type="connection"
                    )
                    connections.append(connection)
                except ValueError:
                    logging.debug(f"Invalid remote port: {remote_port_str}")

    return connections

# --- Collector Classes ---
class HostMetricsCollector:
    """物理主机指标收集器"""

    def __init__(self, hostname: str):
        self.hostname = hostname
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("Initialized HostMetricsCollector.")

    def collect(self):
        """收集物理主机网络连接指标"""
        self.logger.info("Collecting host metrics...")

        # 创建指标容器
        connection_metric = GaugeMetricFamily(
            'network_socket_connection_active',
            'Active network socket connection (1 means active)',
            labels=['instance_type', 'instance_name', 'instance_id', 'hypervisor_hostname',
                    'protocol', 'local_ip', 'local_port', 'remote_ip', 'remote_port', 'state']
        )

        listener_metric = GaugeMetricFamily(
            'network_socket_listener_active',
            'Active network socket listener (1 means active)',
            labels=['instance_type', 'instance_name', 'instance_id', 'hypervisor_hostname',
                    'protocol', 'local_ip', 'local_port']
        )

        try:
            connections = self._get_host_connections()

            for conn in connections:
                common_labels = [
                    'physical',  # instance_type
                    self.hostname,  # instance_name
                    self.hostname,  # instance_id
                    self.hostname,  # hypervisor_hostname
                ]

                if conn.connection_type == "listener":
                    listener_metric.add_metric(
                        common_labels + [
                            conn.protocol,
                            conn.local_ip,
                            str(conn.local_port)
                        ],
                        1
                    )
                else:
                    connection_metric.add_metric(
                        common_labels + [
                            conn.protocol,
                            conn.local_ip,
                            str(conn.local_port),
                            conn.remote_ip,
                            str(conn.remote_port),
                            conn.state
                        ],
                        1
                    )
        except Exception as e:
            self.logger.error(f"Error collecting host metrics: {e}")

        self.logger.info("Finished collecting host metrics.")
        return [connection_metric, listener_metric]

    def _get_host_connections(self) -> List[NetworkConnection]:
        """获取物理主机的网络连接"""
        connections = []
        self.logger.info("_get_host_connections start.")
        try:
            for conn in psutil.net_connections(kind='inet'):

                # 跳过回环地址（如果配置要求）
                if not config.include_loopback and conn.laddr:
                    if conn.laddr.ip.startswith('127.') or conn.laddr.ip == '::1':
                        continue

                # 端口范围过滤
                if config.port_range_filter and conn.laddr:
                    port_min, port_max = config.port_range_filter
                    if not (port_min <= conn.laddr.port <= port_max):
                        continue

                if conn.status == psutil.CONN_NONE:
                    if conn.type == socket.SOCK_DGRAM and conn.laddr and not conn.raddr:
                        # UDP监听
                        if is_illegal_ip(str(conn.laddr.ip)): continue
                        network_conn = NetworkConnection(
                            protocol='udp',
                            local_ip=str(conn.laddr.ip),
                            local_port=conn.laddr.port,
                            connection_type='listener'
                        )
                        connections.append(network_conn)
                    continue

                if conn.laddr and conn.raddr and conn.raddr.ip and conn.raddr.port:
                    # 活跃连接
                    proto = "tcp" if conn.type == socket.SOCK_STREAM else "udp"
                    if conn.status != 'ESTABLISHED': continue
                    if is_illegal_ip(str(conn.laddr.ip)) or is_illegal_ip(str(conn.raddr.ip)): continue
                    network_conn = NetworkConnection(
                        protocol=proto,
                        local_ip=str(conn.laddr.ip),
                        local_port=conn.laddr.port,
                        remote_ip=str(conn.raddr.ip),
                        remote_port=conn.raddr.port,
                        state=conn.status,
                        connection_type='connection'
                    )
                    connections.append(network_conn)
                elif conn.status == psutil.CONN_LISTEN and conn.laddr:
                    # TCP监听
                    if is_illegal_ip(str(conn.laddr.ip)): continue
                    proto = "tcp" if conn.type == socket.SOCK_STREAM else "udp"
                    network_conn = NetworkConnection(
                        protocol=proto,
                        local_ip=str(conn.laddr.ip),
                        local_port=conn.laddr.port,
                        connection_type='listener'
                    )
                    connections.append(network_conn)

        except psutil.AccessDenied:
            self.logger.error("Access denied for psutil.net_connections(). Run exporter with sufficient privileges.")
        except Exception as e:
            self.logger.error(f"Error getting host connections: {e}")

        return connections

class VirtualMachineMetricsCollector:
    """虚拟机指标收集器"""

    def __init__(self, hypervisor_hostname: str):
        self.hypervisor_hostname = hypervisor_hostname
        self.logger = logging.getLogger(self.__class__.__name__)
        self.adapters = self._initialize_adapters()

    def _initialize_adapters(self) -> List[HypervisorAdapter]:
        """初始化虚拟化平台适配器"""
        adapters = []

        try:
            try:
                libvirt_adapter = LibvirtAdapter(self.hypervisor_hostname, config.libvirt_uri)
                if libvirt_adapter.is_available():
                    adapters.append(libvirt_adapter)
                    self.logger.info("Libvirt adapter initialized successfully")
                else:
                    self.logger.warning("Libvirt adapter not available")
            except Exception as e:
                self.logger.error(f"Failed to initialize Libvirt adapter: {e}")

            self.logger.info(f"Initialized {len(adapters)} hypervisor adapters")

        except Exception as e:
            self.logger.error(f"Error initializing adapters: {e}")

        return adapters

    def collect(self):
        """收集虚拟机网络连接指标"""
        self.logger.info("Starting VM metrics collection...")

        # 创建指标容器
        vm_connection = GaugeMetricFamily(
            'vm_network_socket_connection_active',
            'Active VM network connections',
            labels=['hypervisor', 'domain', 'uuid', 'os_type',
                   'protocol', 'local_ip', 'local_port',
                   'remote_ip', 'remote_port', 'state']
        )

        vm_listener = GaugeMetricFamily(
            'vm_network_socket_listener_active',
            'Active VM network listeners',
            labels=['hypervisor', 'domain', 'uuid', 'os_type',
                   'protocol', 'local_ip', 'local_port']
        )

        if not self.adapters:
            self.logger.warning("No hypervisor adapters available")
            return [vm_connection, vm_listener]

        # 遍历所有可用的适配器
        for adapter in self.adapters:
            try:
                vms = adapter.get_vms()
                self.logger.info(f"Found {len(vms)} VMs via {adapter.__class__.__name__}")

                for vm_info in vms:
                    self._process_vm(vm_info, adapter, vm_connection, vm_listener)

            except Exception as e:
                self.logger.error(f"Error processing VMs with {adapter.__class__.__name__}: {e}")

        self.logger.info("Finished VM metrics collection")
        return [vm_connection, vm_listener]

    def _process_vm(self, vm_info: VMInfo, adapter: HypervisorAdapter,
                   connection_metric: GaugeMetricFamily, listener_metric: GaugeMetricFamily):
        """处理单个虚拟机的网络连接"""
        try:
            connections = adapter.get_vm_connections(vm_info)
            self.logger.debug(f"Found {len(connections)} connections for VM {vm_info.name}")

            for conn in connections:
                # 应用过滤器
                if not self._should_include_connection(conn):
                    continue

                common_labels = [
                    self.hypervisor_hostname,
                    vm_info.name,
                    vm_info.id,
                    vm_info.os_type,
                    conn.protocol,
                    conn.local_ip,
                    str(conn.local_port)
                ]

                if conn.connection_type == 'listener':
                    listener_metric.add_metric(common_labels, 1)
                else:
                    full_labels = common_labels + [
                        conn.remote_ip,
                        str(conn.remote_port),
                        conn.state
                    ]
                    connection_metric.add_metric(full_labels, 1)

        except Exception as e:
            self.logger.error(f"Error processing VM {vm_info.name}: {e}")

    def _should_include_connection(self, conn: NetworkConnection) -> bool:
        """判断是否应该包含此连接"""
        # 跳过回环地址
        if not config.include_loopback:
            if conn.local_ip.startswith('127.') or conn.local_ip == '::1':
                return False

        # 端口范围过滤
        if config.port_range_filter:
            port_min, port_max = config.port_range_filter
            if not (port_min <= conn.local_port <= port_max):
                return False

        return True

# --- Main Execution ---
def setup_logging(level: str, format_str: str):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_str,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('/tmp/nodevmport_exporter.log')
        ]
    )

def main():
    """主函数"""
    global config

    parser = argparse.ArgumentParser(description='Prometheus exporter for network connections on host and VMs.')
    parser.add_argument('--port', type=int, help='Port to expose metrics on.')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging.')
    parser.add_argument('--config', type=str, default='/etc/nodevmport_exporter/config.json',
                       help='Configuration file path.')
    parser.add_argument('--generate-config', action='store_true',
                       help='Generate default configuration file and exit.')
    args = parser.parse_args()

    # 生成默认配置文件
    if args.generate_config:
        config_dir = os.path.dirname(args.config)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        config.to_file(args.config)
        print(f"Default configuration saved to {args.config}")
        return

    # 加载配置文件
    if os.path.exists(args.config):
        config = Config.from_file(args.config)
        print(f"Configuration loaded from {args.config}")
    else:
        print(f"Configuration file {args.config} not found, using defaults")

    # 命令行参数覆盖配置文件
    if args.port:
        config.port = args.port
    if args.debug:
        config.debug = True
        config.log_level = "DEBUG"

    # 设置日志
    setup_logging(config.log_level, config.log_format)
    logger = logging.getLogger(__name__)

    current_host = get_current_hostname()
    logger.info(f"Network Exporter starting on host: {current_host}")
    logger.info(f"Configuration: port={config.port}, debug={config.debug}")

    # 初始化收集器
    collectors = []

    if config.enable_host_monitoring:
        host_collector = HostMetricsCollector(current_host)
        collectors.append(host_collector)
        REGISTRY.register(host_collector)
        logger.info("Host metrics collector registered")

    if config.enable_vm_monitoring:
        try:
            logger.info("Initializing VM metrics collector...")
            vm_collector = VirtualMachineMetricsCollector(current_host)
            collectors.append(vm_collector)
            REGISTRY.register(vm_collector)
            logger.info("VM metrics collector registered")
        except Exception as e:
            logger.error(f"Failed to initialize VM metrics collector: {e}")
            logger.warning("Continuing without VM monitoring")

    # 启动HTTP服务器
    logger.info(f"Starting HTTP server on port {config.port}")
    start_http_server(config.port)
    logger.info("Exporter is ready to be scraped.")

    try:
        while True:
            logger.debug(f"Waiting {config.collection_interval}s until next loop...\n\n\n")
            time.sleep(config.collection_interval)

    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down gracefully...")
    except Exception as e:
        logger.error(f"Unexpected error in main loop: {e}", exc_info=True)
    finally:
        logger.info("Exporter shutting down.")

if __name__ == '__main__':
    main()