#!/bin/bash

# 修复后的容器启动脚本
# 解决容器持续重启的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

CONTAINER_NAME="node_port_exporter"
IMAGE_NAME="host_vm_port:v1.0.0"

echo -e "${GREEN}启动修复后的网络监控容器${NC}"
echo "=================================="

# 检查前置条件
check_prerequisites() {
    echo -e "${BLUE}检查前置条件...${NC}"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Docker未安装${NC}"
        exit 1
    fi
    
    # 检查镜像
    if ! docker images | grep -q "$IMAGE_NAME"; then
        echo -e "${RED}镜像 $IMAGE_NAME 不存在${NC}"
        echo "请先构建镜像: docker build -t $IMAGE_NAME ."
        exit 1
    fi
    
    # 检查libvirt
    if ! systemctl is-active --quiet libvirtd; then
        echo -e "${YELLOW}libvirtd服务未运行，尝试启动...${NC}"
        sudo systemctl start libvirtd || {
            echo -e "${RED}无法启动libvirtd服务${NC}"
            exit 1
        }
    fi
    
    echo -e "${GREEN}前置条件检查通过${NC}"
}

# 停止现有容器
stop_existing_container() {
    echo -e "${BLUE}停止现有容器...${NC}"
    
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "发现现有容器，正在停止和删除..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        echo "现有容器已清理"
    else
        echo "没有发现现有容器"
    fi
}

# 创建配置目录和文件

setup_config() {
    echo -e "${BLUE}设置配置...${NC}"
    
    # 创建配置目录
    sudo mkdir -p /etc/network_exporter
    sudo mkdir -p /var/log/network_exporter
    sudo mkdir -p /var/lib/network_exporter
    
    # 复制调试配置文件
    if [[ -f "config_debug.json" ]]; then
        sudo cp config_debug.json /etc/network_exporter/config.json
        echo "已复制调试配置文件"
    elif [[ -f "config.json" ]]; then
        sudo cp config.json /etc/network_exporter/config.json
        echo "已复制默认配置文件"
    else
        echo -e "${YELLOW}未找到配置文件，使用容器内默认配置${NC}"
    fi
    
    # 设置权限
    sudo chmod 755 /etc/network_exporter
    sudo chmod 755 /var/log/network_exporter
    sudo chmod 755 /var/lib/network_exporter
}

# 启动容器
start_container() {
    echo -e "${BLUE}启动容器...${NC}"
    
    # 修复后的启动命令
    docker run \
        --name "$CONTAINER_NAME" \
        --restart=unless-stopped \
        --privileged \
        --network host \
        -v /var/run/libvirt:/var/run/libvirt:ro \
        -v /var/run:/var/run:ro \
        -v /proc:/host_proc:ro \
        -v /etc/network_exporter:/etc/network_exporter:ro \
        -v /var/log/network_exporter:/var/log/network_exporter \
        -v /var/lib/network_exporter:/var/lib/network_exporter \
        -e PYTHONUNBUFFERED=1 \
        -d \
        "$IMAGE_NAME"
    
    echo -e "${GREEN}容器启动命令已执行${NC}"
}

# 检查容器状态
check_container_status() {
    echo -e "${BLUE}检查容器状态...${NC}"
    
    # 等待容器启动
    sleep 5
    
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo -e "${GREEN}✓ 容器正在运行${NC}"
        
        # 检查日志
        echo "容器启动日志:"
        echo "=============="
        docker logs --tail 20 "$CONTAINER_NAME" 2>&1
        echo "=============="
        
        # 检查端口
        sleep 5
        if curl -s http://localhost:9192/metrics > /dev/null; then
            echo -e "${GREEN}✓ 服务端点响应正常${NC}"
        else
            echo -e "${YELLOW}⚠ 服务端点暂未响应，可能还在启动中${NC}"
        fi
        
    else
        echo -e "${RED}✗ 容器启动失败${NC}"
        echo "容器日志:"
        docker logs "$CONTAINER_NAME" 2>&1 || echo "无法获取日志"
        return 1
    fi
}

# 显示使用信息
show_usage() {
    echo
    echo -e "${GREEN}容器启动完成！${NC}"
    echo
    echo "常用命令:"
    echo "  查看日志: docker logs -f $CONTAINER_NAME"
    echo "  查看状态: docker ps | grep $CONTAINER_NAME"
    echo "  停止容器: docker stop $CONTAINER_NAME"
    echo "  重启容器: docker restart $CONTAINER_NAME"
    echo "  查看指标: curl http://localhost:9192/metrics"
    echo "  进入容器: docker exec -it $CONTAINER_NAME /bin/bash"
    echo
    echo "故障排除:"
    echo "  运行故障排除脚本: ./troubleshoot.sh"
    echo "  查看详细日志: docker logs $CONTAINER_NAME"
    echo
}

# 主函数
main() {
    check_prerequisites
    stop_existing_container
    setup_config
    start_container
    
    if check_container_status; then
        show_usage
    else
        echo -e "${RED}容器启动失败，请运行故障排除脚本: ./troubleshoot.sh${NC}"
        exit 1
    fi
}

# 执行主函数
main "$@"
