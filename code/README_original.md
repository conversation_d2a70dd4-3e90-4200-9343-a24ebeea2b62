# 网络端口监控和拓扑生成系统

这是一个用于监控物理机和虚拟机网络端口连接的系统，支持生成网络拓扑图。

## 功能特性

### 核心功能
- **物理机监控**: 监控物理主机的网络连接和监听端口
- **虚拟机监控**: 支持监控Linux和Windows虚拟机的网络连接
- **跨平台支持**: 支持KVM/QEMU、VMware、Hyper-V等虚拟化平台
- **网络拓扑生成**: 自动分析连接关系并生成网络拓扑图
- **Prometheus集成**: 导出指标供Prometheus监控系统使用

### 改进特性
- **QGA集成**: 使用QEMU Guest Agent从虚拟机内部收集网络数据
- **跨平台支持**: 通过QGA支持Linux和Windows虚拟机监控
- **配置文件支持**: 支持JSON格式的配置文件
- **灵活的过滤器**: 支持端口范围、IP地址类型过滤
- **健壮的错误处理**: 完善的异常处理和日志记录
- **模块化设计**: 采用适配器模式支持多种虚拟化平台
- **智能回退**: QGA不可用时自动回退到nsenter方法

## 安装和配置

### 1. 环境要求
- Python 3.9+
- libvirt-python
- psutil
- prometheus-client
- QEMU Guest Agent (在虚拟机内安装)

#### QGA安装
**Linux虚拟机:**
```bash
# Ubuntu/Debian
sudo apt-get install qemu-guest-agent
sudo systemctl enable qemu-guest-agent
sudo systemctl start qemu-guest-agent

# CentOS/RHEL
sudo yum install qemu-guest-agent
sudo systemctl enable qemu-guest-agent
sudo systemctl start qemu-guest-agent
```

**Windows虚拟机:**
1. 下载并安装 QEMU Guest Agent for Windows
2. 确保服务已启动

### 2. Docker部署
```bash
# 构建镜像
docker build -t prometheus_nodevmport_exporter:v1.0.0 .

# 运行容器
docker run --name prometheus_nodevmport_exporter \
  -v /var/run/libvirt:/var/run/libvirt \
  -v /var/run:/var/run \
  -v /proc:/host_proc \
  -v /etc/kolla/prometheus-nodevmport-exporter:/etc/network_exporter \
  --restart=unless-stopped \
  --privileged \
  --network host \
  -d prometheus_nodevmport_exporter:v1.0.0
```

### 3. 配置文件
生成默认配置文件：
```bash
python app.py --generate-config --config /etc/network_exporter/config.json
```

配置文件说明：
- `port`: Prometheus指标导出端口（默认9192）
- `enable_host_monitoring`: 是否启用物理机监控
- `enable_vm_monitoring`: 是否启用虚拟机监控
- `collection_interval`: 拓扑数据收集间隔（秒）
- `include_loopback`: 是否包含回环地址
- `port_range_filter`: 端口范围过滤器 [min_port, max_port]
- `qga_settings`: QGA相关配置
  - `enable_qga`: 是否启用QGA（默认true）
  - `qga_timeout`: QGA命令超时时间（秒）
  - `fallback_to_nsenter`: QGA失败时是否回退到nsenter
  - `retry_attempts`: 重试次数
  - `qga_ping_timeout`: QGA连接测试超时时间

## 指标说明

### 物理机指标
- `network_socket_connection_active`: 活跃的网络连接
- `network_socket_listener_active`: 活跃的监听端口

### 虚拟机指标
- `vm_network_socket_connection_active`: 虚拟机活跃连接
- `vm_network_socket_listener_active`: 虚拟机监听端口

### 标签说明
- `instance_type`: 实例类型（physical/virtual）
- `instance_name`: 实例名称
- `hypervisor_hostname`: 宿主机名称
- `vm_os_type`: 虚拟机操作系统类型
- `protocol`: 协议类型（tcp/udp）
- `local_ip/remote_ip`: 本地/远程IP地址
- `local_port/remote_port`: 本地/远程端口
- `state`: 连接状态