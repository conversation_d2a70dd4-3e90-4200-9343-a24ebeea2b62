#!/usr/bin/env python3
"""
拓扑生成测试脚本
用于验证每30秒生成一次拓扑数据的功能
"""

import json
import os
import time
import threading
from datetime import datetime
from pathlib import Path

def monitor_topology_file(file_path, duration=300):
    """
    监控拓扑文件的变化
    
    Args:
        file_path: 拓扑文件路径
        duration: 监控持续时间（秒）
    """
    print(f"开始监控拓扑文件: {file_path}")
    print(f"监控持续时间: {duration}秒")
    print("-" * 50)
    
    last_modified = 0
    last_generation_time = None
    generation_count = 0
    
    start_time = time.time()
    
    while time.time() - start_time < duration:
        try:
            if os.path.exists(file_path):
                current_modified = os.path.getmtime(file_path)
                
                if current_modified != last_modified:
                    last_modified = current_modified
                    generation_count += 1
                    
                    # 读取拓扑数据
                    with open(file_path, 'r', encoding='utf-8') as f:
                        topology_data = json.load(f)
                    
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    generated_at = topology_data.get('metadata', {}).get('generated_at', 'Unknown')
                    total_nodes = topology_data.get('metadata', {}).get('total_nodes', 0)
                    total_connections = topology_data.get('metadata', {}).get('total_connections', 0)
                    generation_time = topology_data.get('metadata', {}).get('generation_time_seconds', 0)
                    
                    print(f"[{current_time}] 拓扑数据更新 #{generation_count}")
                    print(f"  生成时间: {generated_at}")
                    print(f"  节点数量: {total_nodes}")
                    print(f"  连接数量: {total_connections}")
                    print(f"  生成耗时: {generation_time}秒")
                    
                    # 计算间隔时间
                    if last_generation_time:
                        interval = current_modified - last_generation_time
                        print(f"  距离上次: {interval:.1f}秒")
                    
                    last_generation_time = current_modified
                    print()
            else:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 等待拓扑文件创建...")
        
        except Exception as e:
            print(f"读取拓扑文件时出错: {e}")
        
        time.sleep(1)  # 每秒检查一次
    
    print("-" * 50)
    print(f"监控结束，共检测到 {generation_count} 次拓扑数据更新")

def test_config_interval():
    """测试配置文件中的间隔设置"""
    config_path = "config.json"
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        interval = config.get('collection_interval', 30)
        print(f"配置文件中的收集间隔: {interval}秒")
        
        if interval == 30:
            print("✓ 间隔设置正确（30秒）")
        else:
            print(f"⚠ 间隔设置为 {interval}秒，不是预期的30秒")
        
        topology_enabled = config.get('enable_topology_generation', False)
        if topology_enabled:
            print("✓ 拓扑生成已启用")
        else:
            print("⚠ 拓扑生成未启用")
        
        topology_path = config.get('topology_output_path', '/tmp/network_topology.json')
        print(f"拓扑输出路径: {topology_path}")
        
        return topology_path
    else:
        print(f"配置文件 {config_path} 不存在")
        return "/tmp/network_topology.json"

def create_test_environment():
    """创建测试环境"""
    print("创建测试环境...")
    
    # 确保输出目录存在
    topology_path = "/tmp/network_topology.json"
    os.makedirs(os.path.dirname(topology_path), exist_ok=True)
    
    # 如果存在旧的拓扑文件，先删除
    if os.path.exists(topology_path):
        os.remove(topology_path)
        print(f"已删除旧的拓扑文件: {topology_path}")
    
    return topology_path

def main():
    """主函数"""
    print("=" * 60)
    print("网络拓扑生成测试")
    print("=" * 60)
    
    # 测试配置
    topology_path = test_config_interval()
    print()
    
    # 创建测试环境
    topology_path = create_test_environment()
    print()
    
    # 提示用户启动主程序
    print("请在另一个终端中启动主程序:")
    print("  python app.py --debug")
    print()
    print("或者使用Docker:")
    print("  ./deploy.sh start")
    print()
    
    input("按回车键开始监控拓扑文件生成...")
    print()
    
    # 开始监控
    try:
        monitor_topology_file(topology_path, duration=300)  # 监控5分钟
    except KeyboardInterrupt:
        print("\n监控被用户中断")
    
    print("测试完成")

if __name__ == "__main__":
    main()
